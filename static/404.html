<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="icon" type="image/ico" href="/static/favicon.ico" />
    <link rel="shortcut icon" type="image/ico" href="/static/favicon.ico" />

    <title>Page not found | Just Read</title>

    <link rel="stylesheet" type="text/css" href="/static/theme.css" />

    <style type="text/css">
      .error-page-container {
        max-width: 80%;
        left: 20%;
        position: relative;
      }

      header {
        text-align: center;
        margin-bottom: 10vh;
      }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
    <script>
      window.plausible =
        window.plausible ||
        function () {
          (window.plausible.q = window.plausible.q || []).push(arguments);
        };
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        plausible("404", { props: { path: document.location.pathname } });
      });
    </script>
  </head>
  <body>
    <header>
      <a href="/"><h2>Just Read</h2></a>
    </header>
    <div class="error-page-container">
      <h1>Oops!</h1>
      <h3>We can't find the page you're looking for.</h3>
      <p>Here are some helpful links instead:</p>
      <p><a href="/">Just Read's homepage</a></p>
      <p>
        <a href="https://github.com/ZachSaucier/Just-Read#faq"
          >Just Read's FAQ</a
        >
      </p>
      <p><a href="/support/">Support</a></p>
    </div>
  </body>
</html>
