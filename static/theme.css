:root {
  --jrBlue: #27aae1;
  --jrBlueHover: #4ecaff;
  --secondary: #60739c;
  --missing: #dadada;
  --cta: #002f44;
  --ctaHover: #00a8f3;
}
.lightText {
  color: #ababab;
}

* {
  box-sizing: border-box;
}
body {
  font-family: "Segoe UI", Tahoma, sans-serif;
  margin: 0;
  line-height: 1.5;
  color: #172938;
}
a {
  color: var(--jrBlue);
}

a:hover {
  color: var(--jrBlueHover) !important;
}

button {
  cursor: pointer;
}

code {
  background-color: #eee;
  font-size: 1.2em;
}

.styled button {
  margin-top: 10px;
  border-radius: 100px;
  border: 0;
  background-color: var(--jrBlue);
  outline: none;
  padding: 15px 50px;
  color: white;
  font-weight: bold;
  font-size: inherit;
  transition: 0.1s;
}
.styled.light button {
  background-color: transparent;
  border: 3px solid var(--secondary);
  color: var(--secondary);
}
.styled:hover button {
  background-color: var(--jrBlueHover);
}
.styled.light:hover button {
  background-color: var(--secondary);
  color: white;
}

h4 {
  margin: 0;
  font-size: 1.5em;
}
h5 {
  margin-top: 0;
  font-size: 1.2em;
  font-weight: normal;
}

footer {
  border-top: 1px solid gainsboro;
  padding: 15px 0;
}

footer p {
  margin: 0;
  font-size: 0.9em;
}

header nav a {
  text-decoration: none;
  color: #777;
  display: inline-block;
  height: 100%;
  padding: 27px 12px 24px 12px;
}

header h3 {
  display: inline-block;
}

nav {
  height: 100%;
}
nav ul {
  padding-left: 0;
  height: 100%;
  margin: 0;
}
nav li {
  display: inline-block;
  height: 100%;
}

section {
  padding: 3em;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-logo {
  vertical-align: middle;
  margin-right: 7px;
  margin-top: -5px;
  width: 25px;
}
.site-title {
  font-size: 1.6em;
  margin: 0;
  padding: 20px 0;
}
.site-title a {
  text-decoration: none;
}

.longer-text {
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 300;
}

.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}
.flex-container.centered {
  justify-content: center;
}
.card {
  text-align: left;
  background-color: white;
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
  padding: 35px 35px 15px 35px;
  border-top: 7px solid var(--jrBlue);
}
.card.first {
  margin-right: 40px;
  border-top-color: var(--secondary);
}
.card.first::before {
  color: var(--secondary);
}
.card h2 {
  margin-top: 0;
}
.card ul {
  list-style: none;
  padding: 0;
}
.card li::before {
  content: "\002714";
  color: var(--jrBlue);
  display: inline-block;
  margin-right: 8px;
}
.first li::before {
  color: #bebdd0;
}
.price p {
  margin: 0;
}
.amount {
  font-size: 1.5em;
}

/* Lightbox functionality */
.jr-lightbox {
  display: none;
  position: fixed;
  z-index: 5;
  width: 100%;
  height: 100%;
  text-align: center;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
}
.jr-lightbox > * {
  max-width: 90%;
  max-height: 80%;
  margin-top: 2%;
}
.lightbox-btn {
  transition: color 0.2s;
  color: inherit;
  background: none;
  font: inherit;
  border: 0;
  outline: none;
}
.lightbox-btn:hover {
  color: #5a6a75;
}

.manage {
  font-size: 1.3em;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.missing,
.card .missing::before {
  color: var(--missing);
}

@media screen and (max-width: 1000px) {
  section {
    padding: 2em 0;
  }
  .card {
    display: block;
  }
  .card.first {
    margin-right: 0;
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 610px) {
  .flex-container {
    display: block;
  }
  .site-title {
    display: block;
    text-align: center;
  }
  nav ul {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
  }
}

.gray-section {
  background-color: rgb(248, 248, 248);
}

.youtube-embed {
  width: 100%;
  height: auto;
  aspect-ratio: 640 / 360;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
