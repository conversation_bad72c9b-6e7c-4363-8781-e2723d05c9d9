var progressBar = document.querySelector("progress")
	stylesheet = document.querySelector("link");

function onLoad() {
	document.querySelector(".simple-print").onclick = function() {
		window.print();
	}

	document.querySelectorAll(".back-to-ref").forEach(btn => {
		btn.onclick = function() {
	        window.scrollTo(0, this.dataset.scrollPos);
	    };
	});

	const simpleContainer = document.querySelector(".simple-container");
	function checkBreakpoints() {
        let container = document.querySelector(".simple-article-container");
        if(window.innerWidth - container.offsetWidth < 320) { // Too small to show regular comments
            simpleContainer.classList.add("simple-compact-view");
        } else {
            simpleContainer.classList.remove("simple-compact-view");
        }
	}

	checkBreakpoints();
	window.addEventListener('resize', checkBreakpoints);

	if(progressBar) {
		document.body.classList.add("hideScrollbar");

		var ticking = false;

		var winheight, docheight, trackLength, throttlescroll;
	 
		function getDocHeight() {
		    var D = document;
		    return Math.max(
		        D.body.scrollHeight, D.documentElement.scrollHeight,
		        D.body.offsetHeight, D.documentElement.offsetHeight,
		        D.body.clientHeight, D.documentElement.clientHeight
		    );
		}

		function getMeasurements() {
		    var D = document;
		    winheight = D.defaultView.innerHeight || (D.documentElement || D.body).clientHeight;
		    docheight = getDocHeight();
		    trackLength = docheight - winheight;
    		requestTick();
		}

		function requestTick() {
		    if(!ticking) {
		        requestAnimationFrame(update);
		        ticking = true;
		    }
		}

		function update() {
		    var D = document;
		    var scrollTop = D.defaultView.pageYOffset || (D.documentElement || D.body.parentNode || D.body).scrollTop
		    var pctScrolled = scrollTop / trackLength * 100;
		    var wholePct = Math.floor(pctScrolled);
		    progressBar.value = pctScrolled;
		    
		    ticking = false;
		}

		function initScrollbar() {
		    getMeasurements();
		    window.addEventListener('scroll', requestTick, false); 
		    window.addEventListener('resize', getMeasurements, false);
		}

		initScrollbar();
	}
}

if(document.readyState === 'complete') {
	onLoad();
}

window.onload = onLoad;
