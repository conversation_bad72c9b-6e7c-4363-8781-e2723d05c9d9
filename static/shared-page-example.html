<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width,initial-scale=1">
	<link rel="icon" type="image/ico" href="/favicon.ico">
    <link rel="shortcut icon" type="image/ico" href="/favicon.ico">

	<title>Just Read Shared Example | Just Read</title>

	<link rel="stylesheet" href="/required-styles.css" type="text/css">
</head>
<body>
	<div class="simple-container zachsaucier-com hideScrollbar simple-with-comments"><div class="simple-ui-container"><button class="simple-print simple-control" title="Print article"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64"><path d="M49,0H15v19H0v34h15v11h34V53h15V19H49V0z M17,2h30v17H17V2z M47,62H17V40h30V62z M62,21v30H49V38H15v13H2V21h13h34H62z"></path><rect x="6" y="26" width="4" height="2"></rect><rect x="12" y="26" width="4" height="2"></rect><rect x="22" y="46" width="20" height="2"></rect><rect x="22" y="54" width="20" height="2"></rect></svg>Print</button></div><div class="simple-article-container"><div class="simple-meta"><div class="simple-date"><div>June 5</div></div><div class="simple-author"><div>Zach Saucier</div></div><div class="simple-url"><a class="simple-orig-link" href="https://zachsaucier.com/shared-page.html">https://zachsaucier.com/shared-page.html</a></div><a href="https://zachsaucier.com/shared-page.html" class="original-link">View original page</a><h1 class="simple-title"><div class="">Just Read Shared Example</div></h1></div><div class="content-container">



<p class="">Just Read Premium allows you to share pages in your customized Just Read format along with any annotations, comments, edits, or deletions that you have made.</p>

<p>There are many different annotation options including <span class="jr-highlight-yellow" id="jr-1606075086608">highlighting</span> (even in <span class="jr-highlight-green" id="jr-1606075093475">different</span> <span class="jr-highlight-blue" id="jr-1606075098986">colors</span>!), <span class="jr-bolden" id="jr-1606075106182">bolding</span>, <span class="jr-italicize" id="jr-1606075108372">italicizing</span>, <span class="jr-underline" id="jr-1606075110306">underlining</span>, <span class="jr-strike-through" id="jr-1606075113465">striking through text</span>, and changing the <span class="jr-color-purple" id="jr-1606075120420">text</span> <span class="jr-color-red" id="jr-1606075131739">color</span> (again in <span class="jr-color-pink" id="jr-1606075138010">several</span> <span class="jr-color-orange" id="jr-1606075140831">colors</span>). Use keyboard shortcuts to do any of this. Of course, any deletions and edits of the content will be removed as well. You can even add your own comments!</p>

<h2><a href="https://justread.link/#getJustRead">Get Just Read Premium</a> and start reading and sharing web articles in the way that you want to.</h2>

<p>Other benefits of Just Read Premium include custom scrollbars, auto-scroll functionality, and domain-specific content selectors. Not sure what those are? You can learn about them in <a href="https://github.com/ZachSaucier/Just-Read#faq" target="_blank">Just Read's FAQ section</a>.</p>

<p>Give it a try!</p>

<progress class="simple-progress" max="100" value="0"></progress></div><div class="simple-ext-info"><p class="simple-viewed-using">Viewed using <a href="https://justread.link/" target="_blank">Just Read Premium</a></p><p class="simple-bug-reporter"><a href="https://github.com/ZachSaucier/Just-Read/issues?utf8=%E2%9C%93&amp;q=is%3Aissue%20label%3Abug%20" target="_blank">Report an error</a></p></div></div><div class="simple-compact-comments"><a class="simple-comment-link" href="#jr-1606075159684" style="top: 410px;">[*]</a></div><div class="simple-comments"><div id="jr-1606075159684" class="simple-comment-container jr-posted" style="top: 410px;"><div class="simple-comment-styling"><div class="jr-timestamp">Left on <a href="#jr-1606075159684">11/22/2020 at 14:59</a></div><p class="simple-comment">This is my favorite part!</p><button class="back-to-ref">↑</button></div></div></div><style>@import url('https://fonts.googleapis.com/css?family=Roboto');

body {
    font-family: 'Roboto', BlinkMacSystemFont, sans-serif;
    background-color: #222233;
    line-height: 1.6;
    font-size: 17px;
    color: #D1D1D1;
    text-rendering: optimizeLegibility;
}

@media print {
    body { color: #333; }
}

h1,
h2 {
    font-weight: 700;
}

h1 {
    font-size: 1.875em;
    line-height: 1.125;
}

h2 {
    font-size: 1.45em;
    line-height: 1.2625em;
}

h3 {
    font-size: 1.25em;
    line-height: 1.5;
}

hr {
    height: 1px;
    background-color: inherit;
    border: none;
    width: 100%;
    margin: 0px;
}

a[href] {
    color: #64BEFA;
}

a[href]:hover {
    color: #2089d0;
}

.simple-container {
    -webkit-print-color-adjust: exact;
}

.simple-control,
.simple-control *,
.simple-edit * {
    fill: #D1D1D1;
    color: #D1D1D1;
    border-color: #D1D1D1;
}
.simple-share-dropdown {
    background-color: transparent;
}

.youtubeContainer {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    padding-top: 25px;
}
iframe[src *= "youtube.com/embed/"] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

img {
    max-width: 100%;
}

li {
    line-height: 1.5em;
}

td {
    border: 1px solid black;
    padding: 3px 7px;
}

pre {
    background-color: #E0E0E0;
    color: #1f1f1f;
    padding: 10px;
    overflow: auto;
}

blockquote {
    border-left: 4px solid;
    margin-left: 0;
    padding: 15px 10% 15px 8%;
    margin: 1em 0;
    font-size: 1.2em;
    line-height: 1.4;
}
blockquote > *:first-child {
    margin-top: 0;
}
blockquote > *:last-child {
    margin-bottom: 0;
}

figure {
    margin: 0 0 10px;
}
 figcaption {
    font-size: 0.9em;
    opacity: 0.7;
    border: 1px solid #eee;
    padding: 17px 17px 12px;
}

table {
    background: #004b7a;
    margin: 10px auto;
    border: none;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, .75);
    border-spacing: 0;
    font: inherit;
    text-align: center;
}
table tr td,
table tr th,
table thead th {
    margin: 3px;
    padding: 5px;
    color: #ccc;
    border: 1px solid rgba(255, 255, 255, .25);
    background: rgba(0, 0, 0, .1);
}

aside,
[class *= "sidebar"],
[id *= "sidebar"] {
    max-width: 90%;
    margin: 0 auto;
    border: 1px solid lightgrey;
    padding: 5px 15px;
}

.simple-date {
    display: inline-block;
    font-size: 18px;
    padding-right: 15px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-right: 1px solid #d8d8d8;
}
.rtl .simple-date {
    border-left: 1px solid #d8d8d8;
    border-right: none;
    padding-right: 0;
    padding-left: 15px;
}

.simple-author {
    display: inline-block;
    font-size: 18px;
    color: #27AAE1;
    line-height: 22px;
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
}
.rtl .simple-author {
    padding-left: 0;
    padding-right: 10px;
}

.simple-article-container {
    max-width: 600px;
}

.simple-ext-info {
    border-top-color: #767676;
}
.simple-bug-reporter a[href] {
    color: #e9e9e9;
}
.simple-bug-reporter a[href]:hover {
    color: #fff;
}

.pause-scroll {
    color: #D1D1D1;
}

.simple-progress::-webkit-progress-value {
    background-color: #767676;
}

.simple-found,
.jr-highlight-yellow,
.jr-highlight-green,
.jr-highlight-blue,
.jr-highlight-orange {
    color: #1d1d1d;
}


/* Make emojis and icons a reasonable size */
[class *= "emoji"],
[class *= "icon"]:not([class *= "no-icon"]) { width: 1em; }
[class *= "inline"] { display: inline-block; min-width: 2rem; }
</style><style>.content-container script,.content-container [class="ad"],.content-container [class *="ads"],.content-container [class ^="ad-"],.content-container [class ^="ad_"],.content-container [class *="-ad-"],.content-container [class $="-ad"],.content-container [class $="_ad"],.content-container [class ~="ad"],.content-container [class *="navigation"],.content-container [class *="nav"],.content-container nav,.content-container [class *="search"],.content-container [class *="menu"],.content-container [class *="print"],.content-container [class *="nocontent"],.content-container .hidden,.content-container [class *="popup"],.content-container [class *="share"],.content-container [class *="sharing"],.content-container [class *="social"],.content-container [class *="follow"],.content-container [class *="newsletter"],.content-container [class *="meta"],.content-container [class *="author"],.content-container [id *="author"],.content-container form,.content-container [class ^="form"],.content-container [class *="-form-"],.content-container [class $="form"],.content-container [class ~="form"],.content-container [class *="related"],.content-container [class *="recommended"],.content-container [class *="see-also"],.content-container [class *="popular"],.content-container [class *="trail"],.content-container [class *="comment"],.content-container [class *="disqus"],.content-container [id *="disqus"],.content-container [class ^="tag"],.content-container [class *="-tag-"],.content-container [class $="-tag"],.content-container [class $="_tag"],.content-container [class ~="tag"],.content-container [class *="-tags-"],.content-container [class $="-tags"],.content-container [class $="_tags"],.content-container [class ~="tags"],.content-container [id *="-tags-"],.content-container [id $="-tags"],.content-container [id $="_tags"],.content-container [id ~="tags"],.content-container [class *="subscribe"],.content-container [id *="subscribe"],.content-container [class *="subscription"],.content-container [id *="subscription"],.content-container [class ^="fav"],.content-container [class *="-fav-"],.content-container [class $="-fav"],.content-container [class $="_fav"],.content-container [class ~="fav"],.content-container [id ^="fav"],.content-container [id *="-fav-"],.content-container [id $="-fav"],.content-container [id $="_fav"],.content-container [id ~="fav"],.content-container [class *="favorites"],.content-container [id *="favorites"],.content-container [class *="signup"],.content-container [id *="signup"],.content-container [class *="signin"],.content-container [id *="signin"],.content-container [class *="signIn"],.content-container [id *="signIn"],.content-container footer,.content-container [class *="footer"],.content-container [id *="footer"],.content-container svg[class *="pinterest"],.content-container [class *="pinterest"] svg,.content-container svg[id *="pinterest"],.content-container [id *="pinterest"] svg,.content-container svg[class *="pinit"],.content-container [class *="pinit"] svg,.content-container svg[id *="pinit"],.content-container [id *="pinit"] svg,.content-container svg[class *="facebook"],.content-container [class *="facebook"] svg,.content-container svg[id *="facebook"],.content-container [id *="facebook"] svg,.content-container svg[class *="github"],.content-container [class *="github"] svg,.content-container svg[id *="github"],.content-container [id *="github"] svg,.content-container svg[class *="twitter"],.content-container [class *="twitter"] svg,.content-container svg[id *="twitter"],.content-container [id *="twitter"] svg,.content-container svg[class *="instagram"],.content-container [class *="instagram"] svg,.content-container svg[id *="instagram"],.content-container [id *="instagram"] svg,.content-container svg[class *="tumblr"],.content-container [class *="tumblr"] svg,.content-container svg[id *="tumblr"],.content-container [id *="tumblr"] svg,.content-container svg[class *="youtube"],.content-container [class *="youtube"] svg,.content-container svg[id *="youtube"],.content-container [id *="youtube"] svg,.content-container svg[class *="codepen"],.content-container [class *="codepen"] svg,.content-container svg[id *="codepen"],.content-container [id *="codepen"] svg,.content-container svg[class *="dribble"],.content-container [class *="dribble"] svg,.content-container svg[id *="dribble"],.content-container [id *="dribble"] svg,.content-container svg[class *="soundcloud"],.content-container [class *="soundcloud"] svg,.content-container svg[id *="soundcloud"],.content-container [id *="soundcloud"] svg,.content-container svg[class *="rss"],.content-container [class *="rss"] svg,.content-container svg[id *="rss"],.content-container [id *="rss"] svg,.content-container svg[class *="linkedin"],.content-container [class *="linkedin"] svg,.content-container svg[id *="linkedin"],.content-container [id *="linkedin"] svg,.content-container svg[class *="vimeo"],.content-container [class *="vimeo"] svg,.content-container svg[id *="vimeo"],.content-container [id *="vimeo"] svg,.content-container svg[class *="email"],.content-container [class *="email"] svg,.content-container svg[id *="email"],.content-container [id *="email"] svg{display: none;}.entry-content.entry-content,pre *{display: initial !important;}</style></div>

	<script type="text/javascript" src="/required-scripts.js"></script>
</body>
</html>
