@media screen {
  * {
    box-sizing: border-box;
  }
}
body {
  text-align: center;
  position: relative;
}

.rtl {
  direction: rtl;
  text-align: right;
}

.simple-container {
  max-width: 1000px;
  margin: 0 auto;
  padding-top: 70px;
  padding-bottom: 20px;
  text-align: center;
}
.simple-container > div:not(.button-container) {
  text-align: left;
}
.article-container {
  max-width: 600px;
}
.content-container > *:not(script):not(style) {
  display: block !important;
}

.simple-control {
  position: fixed;
  cursor: pointer;
  transition: 0.2s;

  color: black;
  fill: black;
  opacity: 0.3;
}
.simple-control:hover,
.simple-control.active {
  opacity: 1;
}
.simple-print {
  width: 40px;
  height: 40px;
  top: 10px;
  right: 10px;

  border: 0;
  background: none;
  font-size: 0; /* Hide the print text */
}
.simple-print svg {
  width: 100%;
  height: 100%;
}

.simple-meta > * {
  position: relative;
}
.simple-date:hover .simple-edit,
.simple-author:hover .simple-edit,
.simple-title:hover .simple-edit {
  display: block;
}

.original-link {
  display: block;
  font-family: "Source Sans Pro", sans-serif;
}

.simple-ext-info {
  text-align: center;
  border-top: 1px solid black;
  padding-top: 28px;
}

.simple-viewed-using {
  margin-bottom: 0;
}

.simple-bug-reporter {
  margin-top: 0;
  font-size: 12px;
}
.simple-bug-reporter a[href] {
  color: #282828;
  opacity: 0.5;
}
.simple-bug-reporter a[href]:hover {
  color: #282828;
  opacity: 1;
}

.mwe-math-fallback-image-inline:hover + .simple-plain-text,
.simple-plain-text:hover {
  display: block;
}
.simple-plain-text {
  display: none;
  margin-top: -5px;
}

.simple-article-container {
  display: inline-block;
  vertical-align: top;
}

@media (max-width: 750px) {
  .simple-container {
    padding-left: 50px;
    padding-right: 50px;
  }
}

@media (max-width: 450px) {
  .simple-container {
    padding-top: 30px;
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media print {
  .simple-control,
  .simple-bug-reporter,
  progress,
  .simple-find,
  .pause-scroll,
  .lightbox,
  .original-link,
  .button-container {
    display: none !important;
  }

  *:not(.simple-title *) {
    overflow: auto !important;
  }

  @page :first {
    margin-top: 0;
  }
}

.simple-found,
.jr-highlight-yellow {
  background-color: yellow;
}
.jr-highlight-blue {
  background-color: #00cdff;
}
.jr-highlight-green {
  background-color: #03ff00;
}
.jr-highlight-pink {
  background-color: #ff00bc;
}
.jr-highlight-purple {
  background-color: #8500ff;
}
.jr-highlight-orange {
  background-color: #ff8c2c;
}
.jr-highlight-red {
  background-color: #ec6660;
}
.jr-highlight-white {
  background-color: #fff;
}
.jr-highlight-black {
  background-color: #000;
}

.jr-color-yellow {
  color: #ff0;
}
.jr-color-blue {
  color: #00cdff;
}
.jr-color-green {
  color: #03ff00;
}
.jr-color-pink {
  color: #ff00bc;
}
.jr-color-purple {
  color: #8500ff;
}
.jr-color-orange {
  color: #ff8c2c;
}
.jr-color-red {
  color: #ec6660;
}
.jr-color-white {
  color: #fff;
}
.jr-color-black {
  color: #000;
}

.jr-strike-through {
  text-decoration: line-through;
}
.jr-underline {
  text-decoration: underline;
}
.jr-italicize {
  font-style: italic;
}
.jr-bolden {
  font-weight: bold;
}

.jr-edit-bar {
  border: 1px solid black;
  padding: 5px;
  position: absolute;
  user-select: none;
  z-index: 1;
}
.jr-edit-bar.jr-dark {
  color: #444;
  background-color: #444;
  border: none;
}
.jr-edit-bar::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 100%;
  z-index: -1;
  margin-left: -10px;
  border-style: solid;
  border-width: 10px 10px 0 10px;
  border-color: #c1c1c1 transparent transparent transparent;
}
.jr-dark::after {
  border-color: #444 transparent transparent transparent;
}
.jr-dark button {
  color: #ccc;
}
.jr-edit-bar button {
  background-color: transparent;
  border: none;
  font-size: 20px;
  width: 25px;
  cursor: pointer;
  outline: none;

  padding: 3px;
  fill: currentColor;
}
.jr-edit-bar button:hover {
  color: #fff;
}

.jr-edit-bar svg {
  vertical-align: bottom;
  margin-bottom: 4px;
}

.simple-add-comment svg,
.jr-edit-bar svg {
  pointer-events: none;
}

.jr-edit-bar .text-color {
  padding: 0 6px;
  border-bottom: 3px solid currentColor;
}
.text-color svg {
  margin-left: -3px;
}
.jr-edit-bar .jr-highlight-color {
  padding: 0 4px;
}
.jr-edit-bar .strike {
  padding: 0 3px;
}

.jr-strike svg,
.jr-underline svg,
.jr-highlight-color svg {
  margin-bottom: 3px;
}

.jr-color-picker {
  position: absolute;
  top: 100%;
  width: 81px;
  margin-top: -6px;
  padding: 0px 6px 4px;
  border: 1px solid black;
  display: none;
}
.jr-dark .jr-color-picker {
  border: none;
  background-color: #444;
}
.jr-color-swatch {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  cursor: pointer;
}
.jr-text-picker {
  left: 94px;
}
.jr-highlight-picker {
  left: 123px;
}

/* Comment styles */
.simple-with-comments .simple-comments {
  width: 300px;
}
.simple-comments {
  width: 0;
  text-align: left;
  position: relative;
  display: inline-block;
  vertical-align: top;
  transition: width 0.1s;
}
.simple-compact-comments {
  width: 0;
  position: relative;
  display: none;
}
.simple-compact-view .simple-compact-comments {
  display: inline-block;
}
.simple-compact-view .simple-comment-container {
  position: relative;
  top: 0 !important;
  display: inline-block;
  margin-bottom: 10px;
}
.simple-comment-link {
  text-decoration: none;
  position: absolute;
  left: 0;
}
.back-to-ref {
  display: none;
}
.simple-compact-view .back-to-ref {
  display: block;
}
.simple-comment-container .back-to-ref {
  cursor: pointer;
  padding: 3px;
  line-height: 0.8;
  position: absolute;
  top: 5px;
  right: 5px;
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.rtl .back-to-ref {
  right: auto;
  left: 5px;
}

.simple-comment-container {
  position: absolute;
  top: 0;
  left: 55px;
  z-index: 3;
}
.rtl .simple-comment-container {
  left: 7px;
  text-align: right;
}
.simple-comment-styling {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.2);
  width: 240px;
  padding: 5px;
  background-color: white;
  border: 1px solid lightgrey;
}
.jr-posted {
  z-index: 0;
}
.jr-posted:hover {
  z-index: 1;
}

.jr-timestamp {
  display: none;
  font-size: 0.8em;
  font-family: sans-serif;
  color: grey;
}
.jr-posted .jr-timestamp {
  display: block;
}

.simple-comment {
  color: black;
  font-family: sans-serif;
  line-height: 1.1;
  margin: 0;
}

@supports (mix-blend-mode: lighten) {
  .gradient-text {
    position: relative;
    mix-blend-mode: multiply;
    color: #000 !important;
    background: #fff !important;
  }
  .gradient-text a {
    color: #000 !important;
  }
  .gradient-text.jsGrad::after {
    content: none;
  }
  .gradient-text .colorOverlay,
  .gradient-text::after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    pointer-events: none;
    mix-blend-mode: screen;

    background-repeat: no-repeat;
  }
  .gradient-text img ~ .colorOverlay {
    display: none;
  }
}

.hideScrollbar::-webkit-scrollbar {
  display: none;
}

progress {
  position: fixed;
  top: 0;
  left: 0;
  width: calc(100% + 7px);
  height: 5px;

  -webkit-appearance: none;
  appearance: none;
}
progress::-webkit-progress-bar {
  background-color: transparent;
}
progress::-webkit-progress-value {
  background-color: #5d5d5d;
  border-radius: 100px;
  margin-left: -5px;
}

/* Lightbox functionality */
.jr-lightbox {
  display: none;
  position: fixed;
  z-index: 999;
  width: 100%;
  height: 100%;
  text-align: center;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
}
.jr-lightbox img {
  max-width: 90%;
  max-height: 80%;
  margin-top: 2%;
}
.jr-lightbox:target {
  outline: none;
  display: block;
}
