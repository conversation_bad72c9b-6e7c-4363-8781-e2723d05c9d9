<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="icon" type="image/ico" href="/static/favicon.ico" />
    <link rel="shortcut icon" type="image/ico" href="/static/favicon.ico" />

    <title>Too many views | Just Read</title>

    <link rel="stylesheet" type="text/css" href="/static/theme.css" />

    <style type="text/css">
      .error-page-container {
        max-width: 80%;
        left: 20%;
        position: relative;
      }

      header {
        text-align: center;
        margin-bottom: 10vh;
      }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
    <script>
      window.plausible =
        window.plausible ||
        function () {
          (window.plausible.q = window.plausible.q || []).push(arguments);
        };
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        plausible("429", { props: { path: document.location.pathname } });
      });
    </script>
  </head>
  <body>
    <header>
      <a href="/"><h2>Just Read</h2></a>
    </header>
    <div class="error-page-container">
      <h1>Drats!</h1>
      <h3>
        It seems that people have visited this website more than the alotted
        maximum of 10,000 page views.
      </h3>
      <p>
        In order to avoid this in the future, you can create a copy of your
        shared Just Read page by running Just Read on the page and sharing it
        again when the view count is high. Or you could save the page to your
        computer, Google Drive, or some other platform.
      </p>
      <p>
        If you <i>really</i> need this page, you can request this page to be
        opened again by contacting
        <a
          class="mail-link"
          href="mailto:<EMAIL>?subject=Reopen request&body=Hello,%0D%0A%0D%0AI'd like <JR URL> to be reopened again that was closed because of too many page views.%0D%0A%0D%0AThanks!"
          ><EMAIL></a
        >.
      </p>
      <p>Here are some other links that may be helpful:</p>
      <p><a href="/">Just Read's homepage</a></p>
      <p>
        <a href="https://github.com/ZachSaucier/Just-Read#faq"
          >Just Read's FAQ</a
        >
      </p>
      <p>
        <a href="https://github.com/ZachSaucier/Just-Read/issues">Support</a>
      </p>
    </div>

    <script>
      const mailLink = document.querySelector(".mail-link");
      mailLink.href = `mailto:<EMAIL>?subject=Reopen request&body=Hello,%0D%0A%0D%0AI'd like ${window.location} to be reopened again that was closed because of too many page views.%0D%0A%0D%0AThanks!`;
    </script>
  </body>
</html>
