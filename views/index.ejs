<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="The homepage of Just Read—a customizable reader mode extension."
    />
    <meta name="author" content="<PERSON>" />
    <link rel="icon" type="image/ico" href="/favicon.ico" />
    <link rel="shortcut icon" type="image/ico" href="/favicon.ico" />

    <title>Just Read - Customizable reader mode extension</title>

    <link rel="stylesheet" type="text/css" href="/theme.css" />

    <style type="text/css">
      .notification {
        background-color: #f9d781;
      }

      .splash {
        position: relative;
        text-align: center;
      }
      .splash::after {
        content: "";
        position: absolute;
        top: 0%;
        left: 50%;
        transform: translate(-50%, 0%);
        width: 100%;
        width: min(100%, 1200px);
        max-width: calc(100% - 20px);
        height: 80%;

        border-radius: 25px;
        border-radius: min(25px, 2.5vw);
        background-image: linear-gradient(135deg, #abeeff 0%, #7be5ff 100%);
        z-index: -1;
      }
      .splash img {
        max-width: 600px;
      }

      .inline-svg {
        height: 1.5em;
        vertical-align: bottom;
        margin-bottom: -1px;
        margin-right: 3px;
      }
      button path {
        stroke: #0c435a;
        fill: none;
      }

      .cta {
        padding: 20px 30px;
      }
      .cta path {
        stroke: #eefaff;
      }
      .button-container button {
        padding: 15px 25px;
      }

      .button-container {
        margin: 2em 0;
      }

      .main-heading {
        font-size: 2em;
        max-width: 600px;
        margin: 0 auto;
      }

      .comparison-container {
        --width: 794px;
        --height: 377px;

        position: relative;
        width: var(--width);
        height: var(--height);
        overflow: hidden;
        margin: 0 auto;

        border: 1px solid lightgray;
        border-radius: 10px;
        box-shadow: 0 1px 10px rgba(0, 0, 0, 0.12),
          0 1px 4px rgba(0, 0, 0, 0.24);
      }
      .comparison-image {
        position: absolute;
        height: 100%;
        background-size: var(--width) var(--height);
      }
      .comparison-image.first {
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("/orig2.png");
        background-position: center left;
      }
      .comparison-image.second {
        right: 0;
        width: calc(100% - var(--x, 50%));
        background-image: url("/jrified2.png");
        background-position: center right;
        box-shadow: inset 2px 0 0 #111, -2px 0 0 #111;
      }
      .slider-hint {
        position: absolute;
        left: var(--x);
        top: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        width: 25px;
        height: 25px;
        background-color: black;
        padding: 5px;
      }

      .stat-highlight {
        font-size: 2em;
      }
      .quote {
        display: inline-block;
        font-size: 1em;
        max-width: 350px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.12), 0 1px 4px rgba(0, 0, 0, 0.24);
        padding: 15px;
        vertical-align: middle;
        margin: 15px;
      }
      @media screen and (max-width: 690px) {
        .quote {
          max-width: none;
        }
        .quote:nth-of-type(n + 4) {
          display: none;
        }
      }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
  </head>

  <body>
    <%- include('partials/header'); -%>

    <!-- <section class="notification">
      <div class="container">
        <h2>Notice</h2>
        <p>Currently the sharing premium features of Just Read are down. See <a href="https://github.com/ZachSaucier/Just-Read/issues/297">this GitHub issue</a> for more information and updates.</p>
      </div>
    </section> -->

    <section class="splash">
      <div class="container">
        <% if (!locals.userInfo || !userInfo.isPremium) { %>
        <h2 class="main-heading">
          View web articles in a more readable, attractive, and custom format.
        </h2>
        <% } else { %>
        <h2 class="main-heading">Thanks for supporting Just Read!</h2>
        <% } %>

        <p class="button-container text-center">
          <% if (!locals.userInfo || !userInfo.isPremium) { %>
          <a class="styled cta" href="#getJustRead"
            ><button>
              <svg class="inline-svg" viewBox="0 0 20 20">
                <path
                  d="M10 5.09082V14.0908"
                  stroke-width="2"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M13.2727 10.8182L9.99999 14.091L6.72726 10.8182"
                  stroke-width="2"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19Z"
                  stroke-width="2"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Get Just Read
            </button></a
          >
          <% } %>

          <button class="lightbox-btn">
            <svg class="inline-svg" viewBox="0 0 22 20">
              <path
                d="M14.3333 8.33337L20.1667 5.83337V14.1667L14.3333 11.6667"
                stroke-width="2"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.6667 3.33337H3.5C2.57952 3.33337 1.83333 4.07957 1.83333 5.00004V15C1.83333 15.9205 2.57952 16.6667 3.5 16.6667H12.6667C13.5871 16.6667 14.3333 15.9205 14.3333 15V5.00004C14.3333 4.07957 13.5871 3.33337 12.6667 3.33337Z"
                stroke-width="2"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Watch the video
          </button>
        </p>

        <div class="comparison-container">
          <div class="comparison-image first"></div>
          <div class="comparison-image second"></div>
          <svg class="slider-hint" viewBox="0 0 14 14">
            <path
              d="M4.5 10L2 7L4.5 4"
              stroke="white"
              stroke-width="2"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9.5 10L12 7L9.5 4"
              stroke="white"
              stroke-width="2"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </section>
    <div class="jr-lightbox" id="video-overlay">
      <div id="video"></div>
    </div>

    <% if (!locals.userInfo || !userInfo.isPremium) { %>
    <section id="about-Just-Read">
      <div class="container text-center">
        <h2 class="stat-highlight">Over 250,000 happy users every week.</h2>
        <h5>Here's what a few of them said:</h5>

        <p class="quote">
          "I didn't know I needed this so badly until I used it. Just Read makes
          web articles much more readable and is highly customizable." -
          <i>Laurens De Cock</i>
        </p>
        <p class="quote">
          "Just Read is completely unmatched compared to other reader
          extensions." - <i>Miguel Serra</i>
        </p>
        <p class="quote">
          "This is exactly what I was hoping for - a way to read online articles
          without the annoying ads and to be able to customize the font size and
          content width." - <i>Alex Cohen</i>
        </p>
        <p class="quote">
          "Just Read is an essential addition to Chrome." - <i>Daniel Asher</i>
        </p>
        <p class="quote">
          "This is exactly what I was searching for and it does the job like a
          charm (so easily)! It deserves the 5 stars." - <i>Tanish Bhatt</i>
        </p>

        <h2 class="stat-highlight">
          &#9733;&#9733;&#9733;&#9733;&#9733;
          <div style="font-size: 0.45em; font-weight: lighter">
            in the Chrome web store
          </div>
        </h2>
      </div>
    </section>
    <% } %>

    <section id="getJustRead" class="gray-section">
      <div class="container text-center">
        <% if (!locals.userInfo || !userInfo.isPremium) { %> <% if
        (!locals.userInfo) { %>
        <h2>Choose the Just Read version that's right for you.</h2>
        <% } else { %>
        <h2>Get more features with Premium.</h2>
        <% } %>

        <div class="flex-container centered">
          <div class="card first">
            <h4>Free</h4>
            <div class="price">
              <p><span class="amount">$0.00</span> /mo</p>
            </div>

            <hr />

            <ul>
              <li>Distraction-free and ad-free reading</li>
              <li>Theme customization</li>
              <li>AI-powered summarization</li>
              <li>Auto-run ability</li>
              <li>Deletion of unwanted elements</li>
              <li>Fast support</li>
            </ul>

            <hr />

            <p class="text-center getJustRead">
              <a
                class="styled light"
                id="jrLink"
                href="https://chrome.google.com/webstore/detail/just-read/dgmanlpmmkibanfdgjocnabmcaclkmod"
                ><button>Get free</button></a
              >
            </p>
          </div>

          <div class="card">
            <h4>Premium</h4>
            <div class="price">
              <p>
                <span class="amount">$2</span> /mo
                <span class="lightText">(charged annually)</span>
              </p>
            </div>

            <hr />

            <p style="margin-bottom: 0">
              <strong>Everything in Free <em>plus:</em></strong>
            </p>

            <ul style="margin-top: 0">
              <li>
                Share pages in Just Read format (<a
                  href="/shared-page-example.html"
                  >example</a
                >)
              </li>
              <li>Text annotating and highlighting</li>
              <li>Custom scrollbars</li>
              <li>Auto-scroll functionality</li>
              <li>Domain-specific content selectors</li>
              <!-- <li>HTML Downloads</li> -->
            </ul>

            <hr />

            <% if (locals.userInfo) { %>
            <p class="text-center">
              <a
                class="styled"
                href="https://buy.stripe.com/8wMeWseFS9FI6NaaEF?prefilled_email=<%- userInfo.emailForUrl %>"
                ><button>Get premium</button></a
              >
            </p>
            <% } else { %>
            <p class="text-center">
              <a
                id="getPremiumSignup"
                class="styled"
                href="https://justread.auth.us-east-2.amazoncognito.com/signup?response_type=code&client_id=7veoqii9eb0kft6ffg2vvtskm5&redirect_uri=https://justread.link/dashboard/"
                ><button>Get premium</button></a
              >
            </p>
            <% } %>

            <p class="text-center">Money-back guarantee</p>
          </div>
        </div>
        <% } else { %>
        <div class="flex-container centered">
          <div class="card first text-center">
            <h2>Learn about Just Read's features</h2>
            <a
              class="styled light"
              href="https://github.com/ZachSaucier/Just-Read#faq"
              ><button>See the FAQ</button></a
            >
          </div>

          <div class="card text-center">
            <h2>View your saved entries</h2>
            <a class="styled" href="https://justread.link/dashboard"
              ><button>Go to dashboard</button></a
            >
          </div>
        </div>
        <% } %>

        <h2 style="margin-top: 2em" class="longer-text">
          Learn more about Just Read's features:
        </h2>
        <iframe
          width="640"
          height="360"
          class="youtube-embed"
          src="https://www.youtube-nocookie.com/embed/mKMUXEg873Q"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen=""
        ></iframe>
      </div>
    </section>

    <% if (!locals.userInfo || !userInfo.isPremium) { %>
    <section id="more-info">
      <div class="container text-center">
        <h2 class="longer-text">
          <a
            href="https://github.com/ZachSaucier/Just-Read#using-just-read-features"
            >Feature-packed</a
          >
          and
          <a href="https://github.com/ZachSaucier/Just-Read#privacy-statement"
            >privacy-conscious</a
          >, Just Read is here to help you enjoy reading on the web.
        </h2>
        <h2 class="longer-text">
          That's why Just Read is the best reader mode extension out there.
        </h2>
      </div>
    </section>
    <% } %> <%- include('partials/footer'); -%>

    <script type="text/javascript">
      // Check if Firefox
      if(navigator.userAgent.toLowerCase().indexOf('firefox') > -1 && document.querySelector("#jrLink")) {
          document.querySelector("#jrLink").href = "https://addons.mozilla.org/en-US/firefox/addon/just-read-ext/";
      }
      // Check if Edge
      else if(navigator.userAgent.toLowerCase().indexOf("edg") > -1 && document.querySelector("#jrLink")) {
          document.querySelector("#jrLink").href = "https://microsoftedge.microsoft.com/addons/detail/just-read/knjifalgaonnogbohfflloocfcebopgn";
      }

      var isPremium = false;
      <% if (locals.userInfo) { %>
      isPremium = <%- JSON.stringify(userInfo.isPremium) %>;
      <% } else { %>
      // If they don't have an account but click buy, set cookie so we know to forward later on
      const setCookie = (name, value, days = 7, path = '/') => {
        const expires = new Date(Date.now() + days * 864e5).toUTCString()
        document.cookie = name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=' + path
      }

      document.querySelector("#getPremiumSignup").addEventListener("click", () => {
          setCookie('forward_purchase', true);
      });
      <% } %>

      // See if they have Just Read installed - hide buttons if so
      window.addEventListener("message", (event) => {
        if(event.data.hasJR) {
          document.querySelectorAll(".getJustRead").forEach(elem => {
              const jrLink = elem.querySelector("#jrLink");
              if(jrLink) {
                  jrLink.href = "https://justread.auth.us-east-2.amazoncognito.com/signup?response_type=code&client_id=7veoqii9eb0kft6ffg2vvtskm5&redirect_uri=https://justread.link/dashboard/";
                  jrLink.querySelector("button").innerText = "Sign up";
              } else {
                  elem.style.display = "none";
              }
          });
        }
      }, false);

      // Smooth scroll to getJustRead section
      const getJustReadSection = document.querySelector("#getJustRead");
      document.querySelectorAll("a[href *= '#getJustRead']").forEach(a => a.addEventListener("click", e => {
          e.preventDefault();
          getJustReadSection.scrollIntoView({ behavior: 'smooth' });
      }));



      // Site functionality
      const videoOverlay = document.querySelector("#video-overlay");
      document.querySelector(".lightbox-btn").addEventListener("click", () => {
          videoOverlay.style.display = "block";
          videoOverlay.style.animation = "fadeIn 0.4s";
          setTimeout(() => player.playVideo(), 400);

          videoOverlay.addEventListener("click", () => {
              player.pauseVideo();
              videoOverlay.style.animation = "fadeOut 0.2s";
              videoOverlay.addEventListener("animationend", () => videoOverlay.style.display = "none", { once: true });
          }, { once: true });
      });

      const tag = document.createElement('script');
      tag.src = "https://www.youtube.com/iframe_api";
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
      let player;
      function onYouTubeIframeAPIReady() {
          player = new YT.Player('video', {
              height: '608',
              width: '1080',
              videoId: 'mKMUXEg873Q'
          });
      }


      let x, width;
      const ratio = 794 / 377;
      const elem = document.querySelector('.comparison-container');
      function resize() {
          let winWidth = document.documentElement.clientWidth;

          if(winWidth > 794 + 40)
              winWidth = 794 + 40;

          winWidth -= 40;

          elem.style.setProperty('--width', winWidth + 'px');
          elem.style.setProperty('--height', (winWidth / ratio) + 'px');

          const size = elem.getBoundingClientRect();
          x = size.x;
          width = size.width;
          firstX = width * 0.1;
          secondX = width * 0.9;
      }

      elem.onmouseover = (e) => {
          continueAnimating = false;
      }

      elem.onmousemove = elem.ontouchmove = (e) => {
          const myX = e.clientX || (e.touches[0] ? e.touches[0].clientX : undefined);
          const horizontal = ((myX - x) / width) * 100;
          elem.style.setProperty('--x', horizontal + '%');
      }

      window.onresize = resize;

      const EaseIn    = power => t => Math.pow(t, power),
            EaseOut   = power => t => 1 - Math.abs(Math.pow(t-1, power)),
            EaseInOut = power => t => t<.5 ? EaseIn(power)(t*2)/2 : EaseOut(power)(t*2 - 1)/2+0.5;

      const fps = 60,
          fpsInterval = 1000 / fps,
          totalTime = 3000;

      let startTime,
          now,
          then;

      let firstX, secondX;

      let continueAnimating = true;
      function startAnimating() {
          resize();

          then = Date.now();
          startTime = then;
          animate();
          elem.style.setProperty('--x', firstX + 'px');
      }

      function animate() {
          if(continueAnimating)
              requestAnimationFrame(animate);

          now = Date.now();
          const elapsedSinceLast = now - then;
          if(elapsedSinceLast > fpsInterval) {
              then = now - (elapsedSinceLast % fpsInterval);

              let percent = (now - startTime) / totalTime;

              if(percent > 1) {
                  let tempX = firstX;
                  firstX = secondX;
                  secondX = tempX;
                  startTime = Date.now();
                  percent = 0;
              }

              const horizontal = EaseInOut(4)(1 - percent) * (secondX - firstX) + firstX;

              elem.style.setProperty('--x', horizontal + 'px');
          }
      }
      startAnimating();
    </script>
  </body>
</html>
