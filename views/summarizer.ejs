<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Just Read's summarizer info page." />
    <meta name="author" content="<PERSON>" />
    <link rel="icon" type="image/ico" href="/favicon.ico" />
    <link rel="shortcut icon" type="image/ico" href="/favicon.ico" />

    <title>Summarizer | Just Read</title>

    <link rel="stylesheet" type="text/css" href="/theme.css" />

    <style>
      h1 {
        text-align: center;
      }
      .text-section .container {
        max-width: 700px;
      }
      li {
        margin-bottom: 8px;
      }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
  </head>

  <body>
    <%- include('partials/header'); -%>

    <section class="text-section">
      <div class="container">
        <h1>Summarizer</h1>

        <h2 id="get-started">Getting started</h2>

        <p>
          Just Read's summarizer is powered by OpenAI's
          <a
            href="https://platform.openai.com/docs/api-reference/chat/create"
            target="_blank"
            >chat completion</a
          >
          endpoint.
        </p>

        <p>
          To use the summarizer, you must add your own OpenAI API key to the
          summarizer options within Just Read's options page. Note that this key
          is stored in your browser account data and never shared with anyone
          except OpenAI (not even the Just Read server).
        </p>

        <p>
          If you already have an OpenAI API key, you can use it if you'd like.
        </p>

        <p>
          If you don't have an API key or want to use a separate API key
          specifically for Just Read (which we recommend doing), follow these
          steps:
        </p>

        <ol>
          <li>
            Go to
            <a
              href="https://platform.openai.com/account/api-keys"
              target="_blank"
              >platform.openai.com/account/api-keys</a
            >.
          </li>
          <li>
            Log in or sign up for an OpenAI account. If you're new to OpenAI,
            you can get 3 months free for up to $5.00 in costs. After that,
            you'll have to pay OpenAI to continue using their services.
          </li>
          <li>Click "Create new secret key".</li>
          <li>
            <strong>Make sure to copy and save this key somewhere safe.</strong>
            OpenAI doesn't let you view a key after it has been created, so make
            sure to keep track of it. With that being said, you can create
            multiple keys so if you lose this one you can generate another
            later.
          </li>
          <li>
            Go to the Just Read options page by right clicking the Just Read
            icon and clicking "Options".
          </li>
          <li>
            Under "Summarization options", paste your key where it says
            <code>YOUR_OPENAI_API_KEY_GOES_HERE</code>. Make sure the key you
            provide has quotation marks around it.
          </li>
          <li>
            (optional) If you'd like, you can change the other options like the
            <code>prompt</code>. The parameters available to you are covered
            below.
          </li>
        </ol>

        <h2 id="parameters">Parameters</h2>

        <p>
          Just Read lets you pass in any options that you can normally send into
          OpenAI's chat completion endpoint. The only one that is required is
          the API <code>key</code>. You can change these parameters by going to
          Just Read's options page.
        </p>

        <p>
          Here's a list of the options we think you are most likely to
          potentially change:
        </p>

        <ul>
          <li>
            <p>
              <strong>baseUrl</strong> <em>(string)</em> - The URL you want the
              summarizer request to go to (in the case you want to use something
              other than OpenAI's public endpoint). By default, Just Read
              provides the <code>baseUrl</code> of
              <code>https://api.openai.com/v1/chat/completions</code>
            </p>
          </li>

          <li>
            <p>
              <strong>key</strong> <em>(string) - Required</em> - Your OpenAI
              API key. To get yours, follow the steps above. This goes
              <a
                href="https://github.com/ZachSaucier/Just-Read/blob/5f9d34b0c012ec99dbd842ad631c003e14164ed9/content_script.js#L1353"
                target="_blank"
                >directly from your computer to OpenAI</a
              >
              so there's no security risk in using it, following Just Read's
              <a
                href="https://github.com/ZachSaucier/Just-Read#privacy-statement"
                target="_blank"
                >privacy statement</a
              >.
            </p>
          </li>

          <li>
            <p>
              <strong>model</strong> <em>(string)</em> - The OpenAI chat
              completion model to use. We recommend using
              <code>gpt-3.5-turbo</code>, which is Just Read's default because
              it's cheapest option while good for this sort of task. But you can
              use <code>gpt-4</code> if you'd like.
              <strong>Note:</strong> OpenAI restricts <code>gpt-4</code> to
              users who have made a payment to them.
            </p>

            <p>
              If the selected model does not allow requests large enough to
              handle the summarization request, Just Read will automatically
              attempt to use the model that is able to handle a request of that
              size. For example, if <code>gpt-3.5-turbo</code> is used but the
              request requires more than 4,086 tokens but less than than 16,384
              tokens, <code>gpt-3.5-turbo-16k</code> will be attempted to be
              used. If the request requires between 16,384 and 32,768 tokens,
              <code>gpt-4-32k</code> will be attempted to be used. If the
              request is larger than 32,768 tokens, it's too large for OpenAI
              and will error.
            </p>
          </li>

          <li>
            <p>
              <strong>prompt</strong> <em>(string)</em> - What you want the
              OpenAI model to do. By default, Just Read provides the prompt
              <code
                >Summarize the content you are provided as consisely as possible
                while retaining the key points.</code
              >
              but you can customize this prompt if you'd like. See
              <a href="#prompt-customization"
                >the prompt customization section below</a
              >
              for more details.
            </p>
          </li>

          <li>
            <p>
              <strong>temperature</strong> <em>(number)</em> - This is a number
              between 0 and 1 that affects the "randomness" of the model. The
              closer to 0, the more likely that GPT will choose the same result.
              The closer to 1, the more leeway the model will take. It's common
              to use a temperature around <code>0.3</code>. This defaults to
              <code>0</code>.
            </p>
          </li>

          <li>
            <p>
              <strong>max_tokens</strong> <em>(integer)</em> - This is the max
              number of tokens thats OpenAI is allowed to spend processing the
              request including the prompt, content, and response. Usually an
              article summarization request requires around 1,000-5,000 tokens
              depending on the article length. You can use
              <code>max_tokens</code> to limit how many tokens a request uses
              but note that if the <code>max_tokens</code> amount is reached,
              the summary will likely be cut off mid way or missing completely.
              By default, Just Read doesn't use <code>max_tokens</code>.
            </p>

            <p>
              The main reason why you might want to use this is to disallow Just
              Read from using a model that supports larger requests. For
              example, if <code>gpt-3.5-turbo</code> is selected, it can spend
              up to 4,096 tokens on a given request. If you didn't want a
              request larger than 4,096 tokens to be passed to a model that
              supports larger requests, you could set <code>max_tokens</code> to
              <code>4096</code>.
            </p>

            <p>
              Side note: You can view how many tokens a request took by hovering
              over the "Summary" title if the "Replace the article content with
              the summary." setting is off or in the browser's developer console
              if it is on.
            </p>
          </li>
        </ul>

        <h2 id="prompt-customization">Prompt customization</h2>

        <p>
          While Just Read asks for a concise summary, which ChatGPT responds
          with a paragaph of information, you can prompt it for other things.
          Note that the response that Just Read expects is either HTML or a
          string.
        </p>

        <h3>Examples</h3>

        <p>
          To get it to use bullet points:
          <code
            >Summarize the content you are provided using 3 bullet points. Put
            your response in an HTML format.</code
          >
        </p>

        <p>
          To get it to summarize using a different language, you can either tell
          it to use the language (like
          <code
            >Summarize the content you are provided in German as concisely as
            possible.</code
          >) or just write the prompt in the language you desire the summary in
          (like
          <code
            >Fassen Sie die Inhalte, die Sie erhalten, möglichst prägnant
            zusammen.</code
          >).
        </p>
      </div>
    </section>

    <%- include('partials/footer'); -%>

    <script>
      const optionsLinks = document.querySelectorAll(".options-link");
      // Check if Firefox
      if (navigator.userAgent.toLowerCase().indexOf("firefox") > -1) {
        optionsLinks.forEach(
          (link) =>
            (link.href =
              "moz-extension://80594c36-2567-49d2-8eb8-fde2e9f50f84/options.html")
        );
      }
      // Check if Edge
      else if (navigator.userAgent.toLowerCase().indexOf("edg") > -1) {
        optionsLinks.forEach(
          (link) =>
            (link.href =
              "extension://dgmanlpmmkibanfdgjocnabmcaclkmod/options.html")
        );
      }
    </script>
  </body>
</html>
