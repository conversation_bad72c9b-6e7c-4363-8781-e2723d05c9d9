<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Just Read's user dashboard page.">
    <meta name="author" content="<PERSON>">
    <link rel="icon" type="image/ico" href="/favicon.ico">
    <link rel="shortcut icon" type="image/ico" href="/favicon.ico">

    <title>Your dashboard | Just Read</title>

    <link rel="stylesheet" type="text/css" href="/theme.css">


    <style type="text/css">
        body {
            color: #384047;
        }

        #entry-section {
            padding-top: 0;
        }

        .text-section {
            padding: 0 3em;
            text-align: center;
        }
        @media screen and (max-width: 1000px) {
            .text-section {
                padding: 0;
            }
        }

        table {
            margin: 10px auto;
            font-family: Tahoma, sans-serif;
        }

        caption {
            font-size: 1.6em;
            font-weight: 400;
            padding: 10px 0;
        }

        thead th {
            font-weight: 400;
            background: #8a97a0;
            color: #FFF;
        }

        tr {
            background: #f4f7f8;
            border-bottom: 1px solid #FFF;
            margin-bottom: 5px;
        }

        tr:nth-child(even) {
            background: #e8eeef;
        }

        th, td {
            text-align: left;
            padding: 15px 10px;
            font-weight: 300;
        }

        td a {
            /* Ellipses text after 3 lines */
            display: block;
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        tfoot tr {
            background: none;
        }

        tfoot td {
            padding: 10px 2px;
            font-size: 0.8em;
            font-style: italic;
            color: #8a97a0;
        }

        th[role=columnheader]:not([data-sort-method="none"])::after {
            content: '';
            float: right;
            margin-top: 7px;
            border-width: 0 6px 6px;
            border-style: solid;
            border-color: white transparent;
            visibility: hidden;
            opacity: 0;
            user-select: none;
        }
        th[aria-sort]:not([data-sort-method="none"])::after {
            visibility: visible;
            opacity: 0.4;
        }
        th[role=columnheader]:not([data-sort-method="none"]) {
            cursor: pointer;
        }
        th[aria-sort=ascending]:not([data-sort-method="none"])::after {
            border-bottom: none;
            border-width: 6px 6px 0;
        }
        th[role=columnheader]:not([data-sort-method="none"]):hover::after {
            visibility: visible;
            opacity: 1;
        }

        #signout {
            background: none;
            outline: none;
            border: 1px solid #a2a2a2;
            color: #696868;
            border-radius: 5px;
            font-size: 1.15em;
        }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
</head>

<body>
<%- include('partials/header'); -%>

<% if (locals.justBoughtPremium) { %>
    <h2 class="text-center">Thanks for purchasing Just Read Premium!</h2>

    <script type="text/javascript">
        setTimeout(() => window.postMessage({resetJRLastChecked: true}, "*"), 500);
    </script>
<% } %>

<% if (locals.userInfo) { %>

    <% if (userInfo.managementUrl) { %>
        <p class="manage text-center">To manage your Just Read subscription, view previous purchases, or manage your payment methods <a href="<%- userInfo.managementUrl %>">go here</a>.</p>
    <% } %>

    <% if (userInfo.isPremium) { %>
        <section id="entry-section">
            <table>
                <caption>Your shared Just Read pages</caption>
                <thead>
                    <tr>
                        <th data-sort-default>Original URL</th>
                        <th>Just Read URL</th>
                        <th>Article title</th>
                        <th>Article Author</th>
                        <th>Creation time</th>
                        <th>View count</th>
                        <th data-sort-method='none'><!-- For the delete column --></th>
                    </tr>
                </thead>
                <tbody>
                    <%- pagesHTML %>
                </tbody>
                <tfoot>
                    <tr>
                        <td style="display:none"></td><td colspan="7">Reload the page to refresh the table.</td>
                    </tr>
                </tfoot>
            </table>

            <button id="signout">Sign out</button>
        </section>

    <!-- Not premium -->
    <%} else { %>
        <section class="text-section">
            <div class="container">
                <h2 class="longer-text">Thanks for making an account with Just Read!</h2>
        </section>

        <section id="getJustRead" class="text-center gray-section">
            <% if (!locals.hasOldID) { %>
            <h2 class="longer-text">Want to use <em>all</em> of Just Read's features?<br>Purchase Premium today:</h2>
            <% } %>

            <div class="card" style="display: inline-block; margin-bottom: 3em;">
                <h4>Premium</h4>
                <div class="price">
                    <p><span class="amount">$2</span> /mo <span class="lightText">(charged annually)</span></p>
                </div>

                <hr>

                <ul>
                    <li>Distraction-free and ad-free reading</li>
                    <li>Theme customization</li>
                    <li>Auto-run ability</li>
                    <li>Deletion of unwanted elements</li>
                    <li>Fast support</li>
                    <li>Share pages in Just Read format (<a href="/shared-page-example.html">example</a>)</li>
                    <li>Text annotating and highlighting</li>
                    <li>Custom scrollbars</li>
                    <li>Auto-scroll functionality</li>
                    <li>Domain-specific content selectors</li>
                    <!-- <li>HTML Downloads</li> -->
                </ul>

                <hr>

                <% if (locals.userInfo) { %>
                <p class="text-center"><a class="styled" href="https://buy.stripe.com/8wMeWseFS9FI6NaaEF?prefilled_email=<%- userInfo.emailForUrl %>"><button>Get premium</button></a></p>
                <% } else { %>
                <p class="text-center"><a id="getPremiumSignup" class="styled" href="https://justread.auth.us-east-2.amazoncognito.com/signup?response_type=code&client_id=7veoqii9eb0kft6ffg2vvtskm5&redirect_uri=https://justread.link/dashboard/"><button>Get premium</button></a></p>
                <% } %>

                <p class="text-center">Money-back guarantee</p>
            </div>
        </section>
    <% } %>


<!-- Not logged in -->
<%} else { %>
<section class="text-section">
    <div class="container">
        <h2>In order to see entries, <a href="https://justread.auth.us-east-2.amazoncognito.com/login?response_type=code&client_id=7veoqii9eb0kft6ffg2vvtskm5&redirect_uri=https://justread.link/dashboard/">sign into your Just Read account</a>.</h2>
    </div>
</section>
<% } %>

<%- include('partials/footer'); -%>

<script type="text/javascript" src="/tablesort.min.js"></script>


<script type="text/javascript">
    const searchParams = new URLSearchParams(window.location.search);

    <% if (locals.userInfo && userInfo.isPremium) { %>
    const entryTable = document.querySelector('#entry-section table');
    const dateRegex = /(\d*)-(\d*)-(\d*):(\d*):(\d*):(\d*)/g;
    const sort = new Tablesort(document.querySelector('#entry-section table'));

    Tablesort.extend('customdate', function(item) {
        // Regular expression to test against.
        // `item` is a table value to evaluate.
        dateRegex.lastIndex = 0;
        return dateRegex.test(item);
    }, function(a, b) {
        // Custom sort functionality goes here. Should return 1 or -1
        dateRegex.lastIndex = 0;
        const aGroups = dateRegex.exec(a).slice(1);
        dateRegex.lastIndex = 0;
        const bGroups = dateRegex.exec(b).slice(1);

        let aComesFirst = 1;
        aGroups.some((aGroup, i) => {
            const bGroup = bGroups[i];
            if(aGroup !== bGroup) {
                const aNum = parseInt(aGroup);
                const bNum = parseInt(bGroup);
                aComesFirst = aNum > bNum ? 1 : -1;
                return true;
            }
        });

        return aComesFirst;
    });

    function deleteEntry(delkey, elem) {
        fetch(location.protocol+'//'+location.hostname + "/deleteEntry", {
            method: 'post',
            headers: { "Content-type": "application/json; charset=UTF-8" },
            body: JSON.stringify({
                'delkey': delkey,
                'datetime': elem.querySelector("td:nth-child(5)").innerText,
                'JRUrl': elem.querySelector("td:nth-child(2)").innerText
            })
        })
        .then(function(response) {
            if (!response.ok) throw response;
            else return response.text();
        })
        .then(function(response) {
            elem.parentNode.removeChild(elem);
            sort.refresh();
        })
        .catch(function(error) {
            console.error(`Fetch Error =\n`, error);
        });
    }

    function addDeleteListeners() {
        document.body.addEventListener("click", e => {
            if(e.target.classList.contains("delete")
            && window.confirm("Are you sure you want to delete this page? There is no going back.")) {
                const tr = e.target.parentNode.parentNode;
                deleteEntry(tr.dataset.delkey, tr); // Delete the row and DB entry
            }
        });
    }

    addDeleteListeners();
    <% } %>


    <% if (locals.userInfo) { %>
        const jrSecret = <%- JSON.stringify(userInfo.jrSecret) %>;

        setTimeout(() => window.postMessage({jrSecret}, "*"), 500);

        document.querySelector('#signout').addEventListener('click', () => {
            fetch(location.protocol+'//'+location.hostname + "/signout")
            .then(function(response) {
                if (!response.ok) throw response;
                else location.reload(false);
            })
            .catch(function(error) {
                console.error(`Sign out error =\n`, error);
            });
        });

        <% if (!locals.hasOldID) { %>
            // If no old JR UID, listen for message from extension with UID
            let gotMessage = false;
            window.addEventListener("message", function(event) {
                if(event.data.uid) {
                    gotMessage = true;

                    // Send to server
                    fetch(location.protocol+'//'+location.hostname + "/setUID", {
                        method: 'post',
                        headers: { "Content-type": "application/json; charset=UTF-8" },
                        body: JSON.stringify({
                            'jrSecret': jrSecret,
                            'oldID': event.data.uid,
                        })
                    })
                    .then(function(response) {
                        if (!response.ok) throw response;
                        // worked
                        location.reload();
                    })
                    .catch(function(error) {
                        console.error(`Fetch Error =\n`, error);
                    });
                }
            });
        <% } %>
    <% } %>

    <% if (locals.resetJRLastChecked) { %>
        setTimeout(() => window.postMessage({resetJRLastChecked: true}, "*"), 500);
    <% } %>

    <% if (locals.userInfo && !userInfo.isPremium) { %>
        if (searchParams.get("newsubscription")) {
            // If the redirect from Stripe happened before the JR server updated, try refreshing the page
            setTimeout(() => {
                history.replaceState(null, '', location.pathname);
                location.reload();
            }, 1000);
        }
    <% } %>
</script>


</body>
</html>
