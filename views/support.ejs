<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Just Read's support page." />
    <meta name="author" content="<PERSON>" />
    <link rel="icon" type="image/ico" href="/favicon.ico" />
    <link rel="shortcut icon" type="image/ico" href="/favicon.ico" />

    <title>Support | Just Read</title>

    <link rel="stylesheet" type="text/css" href="/theme.css" />

    <style>
      h1 {
        text-align: center;
      }
      .text-section .container {
        max-width: 700px;
      }
    </style>

    <script
      defer
      data-domain="justread.link"
      src="https://plausible.io/js/script.js"
    ></script>
  </head>

  <body>
    <%- include('partials/header'); -%>

    <!-- <section class="notification">
  <div class="container">
    <h2>Notice</h2>
    <p>Currently the sharing premium features of Just Read are down. See <a href="https://github.com/ZachSaucier/Just-Read/issues/297">this GitHub issue</a> for more information and updates.</p>
  </div>
</section> -->

    <section class="text-section">
      <div class="container">
        <h1>Just Read Support</h1>

        <p>Thanks for using Just Read! We want to serve you however we can.</p>
        <p>To receive help, please follow the instructions below:</p>
        <ol>
          <li>
            Check the
            <a
              target="_blank"
              href="https://github.com/ZachSaucier/Just-Read#faq"
              >Just Read FAQs</a
            >
            to see if your question is already answered there.
          </li>
          <li>
            If you didn't find what you were looking for, try looking through
            <a
              target="_blank"
              href="https://github.com/ZachSaucier/Just-Read/issues?q=is%3Aissue"
              >existing issues</a
            >.
          </li>
          <li>
            If you still didn't find what you were looking for, consider the
            following:
            <ul>
              <li>
                Are you having an issue related to your Just Read Premium
                account?<br />If so, please send an email to
                <EMAIL> with relevent details about your issue.
              </li>
              <li>
                For anything else, please create
                <a
                  target="_blank"
                  href="https://github.com/ZachSaucier/Just-Read/issues/new/choose"
                  >a new issue on GitHub</a
                >.
              </li>
            </ul>
          </li>
        </ol>
      </div>
    </section>

    <%- include('partials/footer'); -%>
  </body>
</html>
