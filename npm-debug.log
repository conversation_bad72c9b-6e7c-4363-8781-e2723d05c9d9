0 info it worked if it ends with ok
1 verbose cli [ '/usr/bin/nodejs', '/usr/bin/npm', 'start' ]
2 info using npm@3.5.2
3 info using node@v4.2.6
4 verbose run-script [ 'prestart', 'start', 'poststart' ]
5 info lifecycle Just-Read-Premium@0.0.1~prestart: Just-Read-Premium@0.0.1
6 silly lifecycle Just-Read-Premium@0.0.1~prestart: no script for prestart, continuing
7 info lifecycle Just-Read-Premium@0.0.1~start: Just-Read-Premium@0.0.1
8 verbose lifecycle Just-Read-Premium@0.0.1~start: unsafe-perm in lifecycle true
9 verbose lifecycle Just-Read-Premium@0.0.1~start: PATH: /usr/share/npm/bin/node-gyp-bin:/mnt/c/Users/<USER>/Documents/GitHub/jr-server/node_modules/.bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/mnt/c/Windows/System32:/mnt/c/Windows:/mnt/c/Windows/System32/wbem:/mnt/c/Windows/System32/WindowsPowerShell/v1.0:/mnt/c/Program Files (x86)/Skype/Phone:/mnt/c/Windows/System32/OpenSSH:/mnt/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/mnt/c/Program Files (x86)/AnonVPN/bin:/snap/bin
10 verbose lifecycle Just-Read-Premium@0.0.1~start: CWD: /mnt/c/Users/<USER>/Documents/GitHub/jr-server
11 silly lifecycle Just-Read-Premium@0.0.1~start: Args: [ '-c', 'node app.js' ]
12 silly lifecycle Just-Read-Premium@0.0.1~start: Returned: code: 1  signal: null
13 info lifecycle Just-Read-Premium@0.0.1~start: Failed to exec start script
14 verbose stack Error: Just-Read-Premium@0.0.1 start: `node app.js`
14 verbose stack Exit status 1
14 verbose stack     at EventEmitter.<anonymous> (/usr/share/npm/lib/utils/lifecycle.js:232:16)
14 verbose stack     at emitTwo (events.js:87:13)
14 verbose stack     at EventEmitter.emit (events.js:172:7)
14 verbose stack     at ChildProcess.<anonymous> (/usr/share/npm/lib/utils/spawn.js:24:14)
14 verbose stack     at emitTwo (events.js:87:13)
14 verbose stack     at ChildProcess.emit (events.js:172:7)
14 verbose stack     at maybeClose (internal/child_process.js:821:16)
14 verbose stack     at Process.ChildProcess._handle.onexit (internal/child_process.js:211:5)
15 verbose pkgid Just-Read-Premium@0.0.1
16 verbose cwd /mnt/c/Users/<USER>/Documents/GitHub/jr-server
17 error Linux 4.4.0-17134-Microsoft
18 error argv "/usr/bin/nodejs" "/usr/bin/npm" "start"
19 error node v4.2.6
20 error npm  v3.5.2
21 error code ELIFECYCLE
22 error Just-Read-Premium@0.0.1 start: `node app.js`
22 error Exit status 1
23 error Failed at the Just-Read-Premium@0.0.1 start script 'node app.js'.
23 error Make sure you have the latest version of node.js and npm installed.
23 error If you do, this is most likely a problem with the Just-Read-Premium package,
23 error not with npm itself.
23 error Tell the author that this fails on your system:
23 error     node app.js
23 error You can get information on how to open an issue for this project with:
23 error     npm bugs Just-Read-Premium
23 error Or if that isn't available, you can get their info via:
23 error     npm owner ls Just-Read-Premium
23 error There is likely additional logging output above.
24 verbose exit [ 1, true ]
