import cluster from "cluster";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { CognitoIdentityProvider } from "@aws-sdk/client-cognito-identity-provider";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { S3, GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import Stripe from "stripe";
import fetch from "node-fetch";
import shortid from "shortid";
import uuidv4 from "uuid/v4.js";
import os from "os";
import cors from "cors";
import cookieParser from "cookie-parser";
import fs from "fs";
import qs from "qs";
import jwt from "jsonwebtoken";
import jwkToPem from "jwk-to-pem";
import schedule from "node-schedule";
import express from "express";
import bodyParser from "body-parser";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const region = { region: "us-east-2" };

const isModeProd = true;

// STRIPE
const stripeApiKey = isModeProd
  ? "***********************************************************************************************************"
  : "sk_test_51HajGpA0Z8WQwZqkh7znHHYQ9rYmpdjNZdgIK63x2O0FCvMJlSnO7Q6d5B1edBWIqAQhR3joQSglS5xMQQ3v108S00wb3nHLa0";
const stripe = new Stripe(stripeApiKey, {
  apiVersion: "2023-08-16",
});
const stripeEndpointSecret = isModeProd
  ? "whsec_MCOl5zpQQUtKRIrNydnZGh34qyfvee6H"
  : "whsec_2jQIKpb8uYkEvCzhwaGkVgVny7QSvFr5";
const stripeManagementUrl =
  "https://billing.stripe.com/p/login/dR64jMau2dhJ1B6eUU";

const ddb = new DynamoDB(region);

const userTable = "JR-USER-INFO";
const sixMonthsMS = 157784760000;
const sevenDaysMS = 604800000;

// SENDINBLUE
const sibAPIKey =
  "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";

// MOONCLERK
const mcKey = "********************************";
const mcEndpoint = "https://api.moonclerk.com/customers/";

// Email helper function
const sendEmail = (email, templateId, sender = "<EMAIL>") => {
  fetch("https://api.sendinblue.com/v3/smtp/email", {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      "api-key": sibAPIKey,
    },
    body: {
      sender: sender,
      replyTo: sender,
      templateId: templateId,
      to: [{ email: email }],
    },
  }).catch((err) =>
    console.error(
      `Failed to send email template ${templateId} to ${email}`,
      err
    )
  );
};

function getJRUserInfoWithEmail(email) {
  // Check to see if has an account
  // Return info if user exists, return false if not
  const params = {
    ExpressionAttributeValues: {
      ":email": {
        S: email,
      },
    },
    IndexName: "email-index",
    KeyConditionExpression: "email = :email",
    TableName: userTable,
  };
  return ddb
    .query(params)
    .then((entry) => {
      if (entry.Count && entry.Items[0]) {
        return entry.Items[0];
      } else {
        return;
      }
    })
    .catch((err) => console.error("Get user from email error", err));
}

// Code to run if we're in the master process
if (cluster.isPrimary) {
  const documentClient = DynamoDBDocument.from(new DynamoDB(region));

  // Count the machine's CPUs
  const cpuCount = os.cpus().length;

  // let lastStripeAccount;
  // let numUsersWithStripeAccount = 0;
  // async function getStripeCustomers() {
  //   const stripeCustomerData = await stripe.customers.list({
  //     starting_after: lastStripeAccount,
  //   });
  //   lastStripeAccount = stripeCustomerData.data.at(-1)?.id;
  //   for (let i = 0; i < stripeCustomerData.data.length; i++) {
  //     const customer = stripeCustomerData.data[i];
  //     const { id: stripeID, email } = customer;
  //     try {
  //       const jrUserInfo = await getJRUserInfoWithEmail(email);
  //       await new Promise((resolve) => setTimeout(resolve, 100)); // a bit of delay to not overload the DB
  //       if (jrUserInfo) {
  //         numUsersWithStripeAccount++;
  //         ddb.updateItem({
  //           Key: {
  //             jrID: {
  //               S: jrUserInfo.jrID.S,
  //             },
  //           },
  //           TableName: userTable,
  //           ExpressionAttributeValues: {
  //             ":stripeID": {
  //               S: `${stripeID}`,
  //             },
  //           },
  //           UpdateExpression: "SET stripeID = :stripeID",
  //         });
  //       }
  //     } catch (err) {
  //       console.error("Getting JR info based on email error", err);
  //     }
  //   }
  //   if (stripeCustomerData.has_more) getStripeCustomers();
  //   else console.log("numUsersWithStripeAccount", numUsersWithStripeAccount);
  // }
  // getStripeCustomers();

  function checkItem(item) {
    const currMS = Date.now();

    const expMsDiff = currMS - new Date(item.expDate).getTime();
    if (Math.abs(expMsDiff) < sevenDaysMS) {
      // If less than 7 days BEFORE expire AND !hasSubscription
      if (!item.hasSubscription && expMsDiff < 0) {
        // Have SiB email them about their need to pay
        sendEmail(item.email, 8);
      }

      // If less than 7 days AFTER expire
      else if (expMsDiff >= 0) {
        // Query MC to see if there's an updated expDate
        fetch(mcEndpoint + item.mcID, {
          headers: {
            accept: "application/vnd.moonclerk+json;version=1",
            authorization: "Token token=" + mcKey,
          },
        })
          .then((res) => res.json())
          .then((data) => {
            // Expiration date has changed
            if (
              data.customer &&
              data.customer.subscription &&
              data.customer.subscription.current_period_end !== item.expDate
            ) {
              // Update account in JR system
              ddb
                .updateItem({
                  Key: {
                    jrID: {
                      S: item.jrID,
                    },
                  },
                  TableName: userTable,
                  ExpressionAttributeValues: {
                    ":isPremium": {
                      BOOL: data.customer.subscription.status === "active",
                    },
                    ":hasSub": {
                      BOOL: data.customer.subscription.status === "active",
                    },
                    ":expDate": {
                      S: data.customer.subscription.current_period_end,
                    },
                  },
                  UpdateExpression:
                    "SET isPremium = :isPremium, hasSubscription = :hasSub, expDate = :expDate",
                })
                .then(() => {
                  if (!data.customer.subscription.status === "active") {
                    // Update SiB
                    fetch(
                      "https://api.sendinblue.com/v3/contacts/" + item.sibID,
                      {
                        method: "PUT",
                        headers: {
                          Accept: "application/json",
                          "Content-Type": "application/json",
                          "api-key": sibAPIKey,
                        },
                        body: {
                          attributes: {
                            HASPREMIUM: false,
                          },
                        },
                      }
                    ).catch((err) =>
                      console.error("Update SIB user error", err)
                    );

                    // Have SiB email them about their premium expiring
                    sendEmail(item.email, 9);
                  }
                })
                .catch((err) => {
                  console.error("Cancel premium error", err);
                });
            }
          });
      } else {
        // console.log("didn't meet if conditions")
      }
    }

    // Send upsell email within a week of their account creation
    const userCreatedDate = item.creationDate || "2020-01-01T00:00:00Z";
    const sinceCreationDiff = currMS - new Date(userCreatedDate).getTime();
    if (
      !item.hasSubscription &&
      sinceCreationDiff < sevenDaysMS &&
      sinceCreationDiff > 0
    ) {
      sendEmail(item.email, 7);
    }

    // Send reminder upsell email around 6 months into their use
    if (
      !item.hasSubscription &&
      sinceCreationDiff < sixMonthsMS &&
      sinceCreationDiff > sixMonthsMS - sevenDaysMS
    ) {
      sendEmail(item.email, 6);
    }
  }

  const scanAll = async (params) => {
    let lastEvaluatedKey = "dummy"; // string must not be empty
    while (lastEvaluatedKey) {
      const data = await documentClient.scan(params);

      // Check each item
      [...data.Items].forEach(checkItem);

      lastEvaluatedKey = data.LastEvaluatedKey;
      if (lastEvaluatedKey) {
        params.ExclusiveStartKey = lastEvaluatedKey;
      }
    }
  };

  const scanItems = function () {
    // Iterate through every JR user
    scanAll({
      TableName: userTable,
      ExpressionAttributeNames: {
        "#isPremium": "isPremium",
      },
      ExpressionAttributeValues: {
        ":isPremium": true,
      },
      FilterExpression: "#isPremium = :isPremium",
    }).catch((err) => console.error("Scan error", err));
  };

  // Run this function every Sunday at 9:50pm
  const j = schedule.scheduleJob(
    { hour: 21, minute: 50, dayOfWeek: 0 },
    scanItems
  );

  // Create a worker for each CPU
  for (let i = 0; i < cpuCount; i += 1) {
    cluster.fork();
  }

  // Listen for terminating workers
  cluster.on("exit", function (worker) {
    // Replace the terminated workers
    console.error("Worker " + worker.id + " died :(");
    cluster.fork();
  });

  // Code to run if we're in a worker process
} else {
  const s3 = new S3(region);

  // class Plausible {
  //   domain = "";

  //   constructor(domain) {
  //     this.domain = domain;
  //   }

  //   trackPageview(req, rest) {
  //     this.trackEvent("pageview", req, rest);
  //   }

  //   trackEvent(name, req, { props, revenue } = {}) {
  //     const body = {
  //       domain: this.domain,
  //       name: name,
  //       url: `${req.protocol}://${req.get("host")}${req.originalUrl}`,
  //       referrer: req.get("Referrer"),
  //     };

  //     if (props) {
  //       body.props = props;
  //     }

  //     if (revenue) {
  //       body.revenue = revenue;
  //     }

  //     fetch("https://plausible.io/api/event", {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //         "X-Forwarded-For": req.socket.remoteAddress,
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(body),
  //     }).catch((err) => console.error("Plausible error", err));
  //   }
  // }

  // const plausible = new Plausible("justread.link");

  // DB
  const BUCKET_NAME = "just-read";
  const entryTable = "JR-ENTRY-INFO";
  const MAX_VIEW_COUNT = 10000;
  const MAX_ENTRY_COUNT = 300;
  const POST_LIMIT = "1mb";

  // COGNITO
  const cognitoISP = new CognitoIdentityProvider(region);
  const cognitoClientID = "7veoqii9eb0kft6ffg2vvtskm5";
  const cognitoSecret = "1n39kakbp6jn0566j9oir5d7r5cqjsdq5mlm1posda8vcae0dj5f";
  const jsonWebKeys = [
    {
      alg: "RS256",
      e: "AQAB",
      kid: "C+moPlLOkEa2ta7jp/VPg6rZ9DmwFuVdKpoVHXbTgmo=",
      kty: "RSA",
      n: "lZ-f8ZYee_JbzOfzHsLnX7VLyo8k7ZiZH_3FRXnCVIMJxa7ULAkyep5-P0Zq4H9rF4Epz-E5yLfXFUqR7ndOx3nj5BUCdjlQafwUKzK6vqFXn_9O1O8cbfsPcdFwgVqZZBXV4tg43ySKVIPSf1f_qWFLuXxA4wnYufMJisqAnH8xHAqHPqHrvx8KHBR5ia04wZzYlNswpOoDa4EVpl2bSLhtv9Xgr1rf-Jb7Xtb6Uz5_ttQsCPsWNDXatst1woeG2jZwkyFoT5VrRA4sqOT52tIiX3LKVaoVMHyuq76AKhmWnoA1VqfJ5QKBuLOoCHK-YznU8I-nB8A6RJlZARZ1ZQ",
      use: "sig",
    },
    {
      alg: "RS256",
      e: "AQAB",
      kid: "t0yT+Uge7fEUrbIWaujhPeYBuhcjw/UDMdkQ9BxR+As=",
      kty: "RSA",
      n: "t5rjAp0CVgdwdObMGAlLM2Vki3K3JU0HGVOLAvzC3GkAvJAeVkO8Ts4ZUNZa-n9wkm6-TQ0iVUc7dSVMucsUB7fyBcLizoIpXxnoG9j3xqAdjK7Ie9eZtHkMN55kHjZnPeyurUpQ9yAFauNC2OIV4hj_UEz1c533E6e6X8MzlHL3EnTdqJgyPZP6d4e4drIFcHFABFKaDPXYsr99s1aztNygj3iAcXTcay_xSMB7C2tFDC_3T5G40Ad_mXNemnEUDD5jSteKf4C8w1x3f8cH6f-Bnxy0CLRPuan3TZaImPV5vfI3zb76pRX9IfBjsywtWL59ZwHbXm1ETQSKmjxZQw",
      use: "sig",
    },
  ];

  // COOKIES
  const cookieConfig = {
    httpOnly: true, // disable accessing cookie via client side js
    secure: true, // force https
    maxAge: 2592000000, // ttl in ms (30 days)
  };

  // EXPRESS
  const app = express();
  app.set("view engine", "ejs");
  app.set("views", __dirname + "/views");
  app.use("/stripe-endpoint", express.raw({ type: "application/json" }));
  app.use(
    bodyParser.urlencoded({
      extended: true,
    })
  );
  app.use(
    bodyParser.json({
      limit: POST_LIMIT,
    })
  );
  app.use(express.static(__dirname + "/static"));
  app.use(
    bodyParser.urlencoded({
      limit: POST_LIMIT,
      extended: true,
    })
  );
  app.use(cookieParser());

  app.options("*", cors());

  // COGNITO/AUTH
  function validateToken(body) {
    if (
      body.id_token ||
      (body.AuthenticationResult && body.AuthenticationResult.IdToken)
    ) {
      const id_token =
        body.id_token ||
        (body.AuthenticationResult && body.AuthenticationResult.IdToken);
      const header = decodeTokenHeader(id_token);
      const jsonWebKey = getJsonWebKeyWithKID(header.kid);
      return verifyJsonWebTokenSignature(
        id_token,
        jsonWebKey,
        (err, decodedToken) => {
          if (err) return console.error("verification error", err);

          if (body.refresh_token) {
            decodedToken.refresh_token = body.refresh_token;
          }
          return decodedToken;
        }
      );
    } else {
      throw "Invalid token";
    }
  }

  function decodeTokenHeader(token) {
    const [headerEncoded] = token.split(".");
    const buff = Buffer.from(headerEncoded, "base64");
    const text = buff.toString("ascii");
    return JSON.parse(text);
  }

  function getJsonWebKeyWithKID(kid) {
    for (let jwk of jsonWebKeys) {
      if (jwk.kid === kid) {
        return jwk;
      }
    }
    return null;
  }

  function verifyJsonWebTokenSignature(token, jsonWebKey, clbk) {
    const pem = jwkToPem(jsonWebKey);
    return jwt.verify(
      token,
      pem,
      {
        algorithms: ["RS256"],
      },
      (err, decodedToken) => clbk(err, decodedToken)
    );
  }

  function getCognitoUserInfo(code) {
    return fetch(
      "https://justread.auth.us-east-2.amazoncognito.com/oauth2/token",
      {
        method: "post",
        headers: {
          "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
          authorization:
            "Basic " +
            Buffer.from(cognitoClientID + ":" + cognitoSecret).toString(
              "base64"
            ),
        },
        body: qs.stringify({
          grant_type: "authorization_code",
          code: code,
          redirect_uri: "https://justread.link/dashboard/",
        }),
      }
    )
      .then((res) => res.json())
      .then((body) => validateToken(body))
      .catch((err) => console.error("get cognito info error", err, code));
  }

  function handleCognitoSuccessfulLogin(cognitoUserInfo) {
    return getJRUserInfo(cognitoUserInfo["cognito:username"]).then(
      (jrUserInfo) => {
        if (jrUserInfo.Count) {
          // console.log("user found", jrUserInfo.Items[0]);
          return jrUserInfo.Items[0];
        } else {
          // console.log("no user info found - creating user");
          return createUser(cognitoUserInfo);
        }
      }
    );
  }

  function getJRUserInfo(cognitoID) {
    // Check to see if has an account
    // Return info if user exists, return false if not
    const params = {
      ExpressionAttributeValues: {
        ":cognitoID": {
          S: cognitoID,
        },
      },
      IndexName: "cognitoID-index",
      KeyConditionExpression: "cognitoID = :cognitoID",
      TableName: userTable,
    };
    return ddb.query(params);
  }

  function getUserInfoFromSecret(jrSecret) {
    return ddb
      .query({
        ExpressionAttributeValues: {
          ":jrSecret": {
            S: jrSecret,
          },
        },
        IndexName: "jrSecret-index",
        KeyConditionExpression: "jrSecret = :jrSecret",
        TableName: userTable,
      })
      .then((entry) => {
        if (entry.Count && entry.Items[0]) {
          return entry.Items[0];
        } else {
          return;
        }
      })
      .catch((err) => console.error("Get user from secret error", err));
  }

  function getUserInfoFromStripeID(stripeID) {
    return ddb
      .query({
        ExpressionAttributeValues: {
          ":stripeID": {
            S: stripeID,
          },
        },
        IndexName: "stripeID-index",
        KeyConditionExpression: "stripeID = :stripeID",
        TableName: userTable,
      })
      .then((entry) => {
        if (entry.Count && entry.Items[0]) {
          return entry.Items[0];
        } else {
          return;
        }
      })
      .catch((err) => console.error("Get user from stripe error", err));
  }

  // USER CREATION
  function createSIBUser(cognitoUserInfo) {
    const headers = new fetch.Headers();
    headers.append("Accept", "application/json");
    headers.append("Content-Type", "application/json");
    headers.append("api-key", sibAPIKey);

    const body = JSON.stringify({
      email: cognitoUserInfo.email,
      attributes: {
        HASPREMIUM: false,
      },
    });

    return fetch("https://api.sendinblue.com/v3/contacts", {
      method: "POST",
      headers: headers,
      body: body,
    })
      .then((res) => res.json())
      .then((json) => "" + json.id)
      .catch((err) => console.error("create SIB user error", err));
  }

  function createUser(cognitoUserInfo) {
    return createSIBUser(cognitoUserInfo).then((sibID) => {
      // Send welcome email
      sendEmail(cognitoUserInfo.email, 3);

      // Update our DB
      const jrID = "JR-" + Date.now(); // Generate a unique ID based on the time
      const jrSecret = "JR-" + uuidv4(); // Generate a secret to use (shared with client)

      const entry = {
        email: { S: cognitoUserInfo.email },
        cognitoID: { S: cognitoUserInfo["cognito:username"] },
        sibID: { S: sibID },
        jrID: { S: jrID },
        jrSecret: { S: jrSecret },
        isPremium: { BOOL: false },
        creationDate: { S: new Date().toISOString() },
      };

      return ddb
        .putItem({
          TableName: userTable,
          Item: entry,
        })
        .then((data) => entry)
        .catch((err) => console.error("Create user error", err));
    });
  }

  // AWS FILE MANAGEMENT
  function copyFileFromToBucket(
    sourceName,
    sourceBucket,
    targetName,
    targetBucket,
    cb
  ) {
    let cbCalled = false;

    s3.copyObject(
      {
        CopySource: sourceBucket + "/" + sourceName,
        Bucket: targetBucket,
        Key: targetName,
      },
      function (copyErr, copyData) {
        if (copyErr) {
          done(copyErr);
        } else {
          done();
        }
      }
    );

    function done(err) {
      if (!cbCalled) {
        cb(err);
        cbCalled = true;
      }
    }
  }

  function getEntryFromUrl(jrUrl, cb) {
    const params = {
      ExpressionAttributeValues: {
        ":url": {
          S: jrUrl,
        },
      },
      IndexName: "JRUrl-index",
      KeyConditionExpression: "JRUrl = :url",
      TableName: entryTable,
    };
    ddb.query(params, function (err, data) {
      if (err) console.error("Get entry error", err);
      else if (data) cb(data.Items[0]);
    });
  }

  function getEntryCount(jrID) {
    return ddb
      .query({
        ExpressionAttributeValues: {
          ":jrID": {
            S: jrID,
          },
        },
        KeyConditionExpression: "UserID = :jrID",
        TableName: entryTable,
        Select: "COUNT",
      })
      .then((data) => parseInt(data["Count"]));
  }

  // PAGE RENDERING
  function getSharedPagesHTML(jrUserInfo) {
    const jrID = jrUserInfo.jrID.S;

    if (jrUserInfo.oldID) {
      const params1 = {
        ExpressionAttributeValues: {
          ":id": {
            S: jrID,
          },
        },
        KeyConditionExpression: "UserID = :id",
        TableName: entryTable,
      };

      return ddb
        .query(params1)
        .then((data) => {
          let html = "";

          data.Items.forEach(function (element, index, array) {
            html += `<tr data-delkey="${jrUserInfo.jrSecret.S}">`;
            html +=
              "<td><a href='" +
              element["origURL"]["S"] +
              "' target='_blank'>" +
              element["origURL"]["S"] +
              "</a></td>";
            html +=
              "<td><a href='" +
              element["JRUrl"]["S"] +
              "' target='_blank'>" +
              element["JRUrl"]["S"] +
              "</a></td>";
            html += "<td>" + element["title"]["S"] + "</td>";
            html += "<td>" + element["author"]["S"] + "</td>";
            html += "<td>" + element["datetime"]["S"] + "</td>";
            html += "<td>" + element["viewcount"]["N"] + "</td>";
            html += "<td><button class='delete'>Delete</button></td></tr>";
          });

          return html;
        })
        .then((firstHTML) => {
          const params2 = {
            ExpressionAttributeValues: {
              ":uid": {
                S: jrUserInfo.oldID.S,
              },
            },
            KeyConditionExpression: "UserID = :uid",
            TableName: entryTable,
          };

          return ddb.query(params2).then((data) => {
            let html = "";

            data.Items.forEach(function (element, index, array) {
              html += `<tr data-delkey="${jrUserInfo.oldID.S}">`;
              html +=
                "<td><a href='" +
                element["origURL"]["S"] +
                "' target='_blank'>" +
                element["origURL"]["S"] +
                "</a></td>";
              html +=
                "<td><a href='" +
                element["JRUrl"]["S"] +
                "' target='_blank'>" +
                element["JRUrl"]["S"] +
                "</a></td>";
              html += "<td>" + element["title"]["S"] + "</td>";
              html += "<td>" + element["author"]["S"] + "</td>";
              html += "<td>" + element["datetime"]["S"] + "</td>";
              html += "<td>" + element["viewcount"]["N"] + "</td>";
              html += "<td><button class='delete'>Delete</button></td></tr>";
            });

            if (data.Items.length === 0 && firstHTML === "")
              html =
                '<tr><td colspan="7" style="text-align: center">No entries to show. To learn how to share a page, see "How can I share my Just Read version of a page with someone?" in <a href="https://github.com/ZachSaucier/Just-Read#faq">the Just Read FAQ</a>.</td></tr>';

            return firstHTML + html;
          });
        });
    } else {
      const params = {
        ExpressionAttributeValues: {
          ":id": {
            S: jrUserInfo.jrID.S,
          },
        },
        KeyConditionExpression: "UserID = :id",
        TableName: entryTable,
      };

      return ddb
        .query(params)
        .then((data) => {
          let html = "";

          data.Items.forEach(function (element, index, array) {
            html += `<tr data-delkey="${jrUserInfo.jrSecret.S}">`;
            html +=
              "<td><a href='" +
              element["origURL"]["S"] +
              "' target='_blank'>" +
              element["origURL"]["S"] +
              "</a></td>";
            html +=
              "<td><a href='" +
              element["JRUrl"]["S"] +
              "' target='_blank'>" +
              element["JRUrl"]["S"] +
              "</a></td>";
            html += "<td>" + element["title"]["S"] + "</td>";
            html += "<td>" + element["author"]["S"] + "</td>";
            html += "<td>" + element["datetime"]["S"] + "</td>";
            html += "<td>" + element["viewcount"]["N"] + "</td>";
            html += "<td><button class='delete'>Delete</button></td></tr>";
          });

          if (data.Items.length === 0)
            html =
              "<tr><td colspan='7' style='text-align: center'>No entries to show.</td></tr>";

          return html;
        })
        .catch((err) => console.error("DB query error ", err));
    }
  }

  function renderPageWithLogin(req, res, page) {
    // plausible.trackPageview(req);
    if (
      req.cookies.refresh_token &&
      req.cookies.refresh_token !== "undefined"
    ) {
      const params = {
        AuthFlow: "REFRESH_TOKEN_AUTH",
        ClientId: cognitoClientID,
        AuthParameters: {
          REFRESH_TOKEN: req.cookies.refresh_token,
          SECRET_HASH: cognitoSecret,
        },
      };
      cognitoISP.initiateAuth(params, function (err, data) {
        if (err) console.error("Refresh token issue", err.stack);
        else {
          const cognitoUserInfo = validateToken(data);
          handleCognitoSuccessfulLogin(cognitoUserInfo).then((jrUserInfo) => {
            const displayedData = {
              email: jrUserInfo.email.S,
              emailForUrl: encodeURIComponent(jrUserInfo.email.S),
              isPremium: jrUserInfo.isPremium.BOOL,
              jrSecret: jrUserInfo.jrSecret.S,
            };
            if (jrUserInfo.mcURL) {
              displayedData.managementUrl = jrUserInfo.mcURL.S;
            } else if (jrUserInfo.isPremium.BOOL) {
              displayedData.managementUrl = stripeManagementUrl;
            }

            if (page === "dashboard") {
              getSharedPagesHTML(jrUserInfo).then((pagesHTML) => {
                if (req.query.jrSecret) {
                  setTimeout(() => {
                    res.render(page, {
                      userInfo: displayedData,
                      pagesHTML: pagesHTML,
                      hasOldID: typeof jrUserInfo.oldID !== "undefined",
                      resetJRLastChecked: true,
                      justBoughtPremium: true,
                    });
                  }, 1000);
                } else {
                  res.render(page, {
                    userInfo: displayedData,
                    pagesHTML: pagesHTML,
                    hasOldID: typeof jrUserInfo.oldID !== "undefined",
                  });
                }
              });
            } else {
              res.render(page, {
                userInfo: displayedData,
                hasOldID: typeof jrUserInfo.oldID !== "undefined",
              });
            }
          });
        }
      });
    } else if (req.query.code) {
      getCognitoUserInfo(req.query.code).then((cognitoUserInfo) => {
        if (!cognitoUserInfo) {
          res.redirect("https://justread.link/dashboard");
          return;
        }

        handleCognitoSuccessfulLogin(cognitoUserInfo).then((jrUserInfo) => {
          const displayedData = {
            email: jrUserInfo.email.S,
            emailForUrl: encodeURIComponent(jrUserInfo.email.S),
            isPremium: jrUserInfo.isPremium.BOOL,
            jrSecret: jrUserInfo.jrSecret.S,
          };
          if (jrUserInfo.mcURL) {
            displayedData.managementUrl = jrUserInfo.mcURL.S;
          } else if (jrUserInfo.isPremium.BOOL) {
            displayedData.managementUrl = stripeManagementUrl;
          }

          if (page === "dashboard") {
            if (req.cookies.forward_purchase) {
              res.cookie(
                "refresh_token",
                cognitoUserInfo.refresh_token,
                cookieConfig
              );
              res.clearCookie("forward_purchase");
              res.redirect(
                `https://buy.stripe.com/8wMeWseFS9FI6NaaEF?prefilled_email=${encodeURI(
                  jrUserInfo.email.S
                )}`
              );
            } else {
              getSharedPagesHTML(jrUserInfo).then((pagesHTML) => {
                // Update the refresh token cookie (to save login longer)
                res.cookie(
                  "refresh_token",
                  cognitoUserInfo.refresh_token,
                  cookieConfig
                );

                res.render(page, {
                  userInfo: displayedData,
                  pagesHTML: pagesHTML,
                  resetJRLastChecked: true,
                  hasOldID: typeof jrUserInfo.oldID !== "undefined",
                });
              });
            }
          } else {
            res.render(page, {
              userInfo: displayedData,
              hasOldID: typeof jrUserInfo.oldID !== "undefined",
            });
          }
        });
      });
    } else {
      // Not logged in yet
      res.render(page);
    }
  }

  // REQUEST HANDLING
  app.get("/", function (req, res) {
    renderPageWithLogin(req, res, "index");
  });

  app.get("/signout", function (req, res) {
    res.clearCookie("refresh_token");
    res.redirect("https://justread.link/dashboard");
  });

  app.get("/dashboard", function (req, res) {
    renderPageWithLogin(req, res, "dashboard");
  });

  app.get("/support", function (req, res) {
    renderPageWithLogin(req, res, "support");
  });

  app.get("/summarizer", function (req, res) {
    renderPageWithLogin(req, res, "summarizer");
  });

  app.post("/checkPremium", function (req, res) {
    res.setHeader("Access-Control-Allow-Origin", "*");

    if (req.body.jrSecret && req.body.jrSecret !== "undefined") {
      getUserInfoFromSecret(req.body.jrSecret).then((userInfo) => {
        if (userInfo) {
          res.send(userInfo.isPremium.BOOL);
        } else {
          res.send(false);
        }
      });
    } else {
      res.send(false);
    }
  });

  app.post("/setUID", function (req, res) {
    if (req.body.oldID && req.body.jrSecret) {
      getUserInfoFromSecret(req.body.jrSecret).then((userInfo) => {
        const oldID = "JR-" + req.body.oldID;

        // Make sure no user already has this old ID
        ddb
          .query({
            ExpressionAttributeValues: {
              ":oldID": {
                S: oldID,
              },
            },
            IndexName: "oldID-index",
            KeyConditionExpression: "oldID = :oldID",
            TableName: userTable,
          })
          .then((entry) => {
            if (entry.Count === 0) {
              ddb.updateItem(
                {
                  Key: {
                    jrID: {
                      S: userInfo.jrID.S,
                    },
                  },
                  TableName: userTable,
                  ExpressionAttributeValues: {
                    ":oldID": {
                      S: oldID,
                    },
                  },
                  UpdateExpression: "SET oldID = :oldID",
                },
                function (err, data) {
                  if (err) {
                    const returnStatus = 500;

                    if (err.code === "ConditionalCheckFailedException") {
                      returnStatus = 409;
                    }

                    res.status(returnStatus).end();
                    console.error("Add old ID error", err);
                  } else {
                    // Updated DB correctly - Refresh page
                    res.redirect("/dashboard");
                  }
                }
              );
            } else {
              res.status(409).end();
              return;
            }
          })
          .catch((err) => console.error("Set oldID error", err));
      });
    }
  });

  app.post("/hook/plan-created", function (req, res) {
    const data = req.body.data;
    getJRUserInfoWithEmail(data.email)
      .then((jrUserInfo) => {
        ddb
          .updateItem({
            Key: {
              jrID: {
                S: jrUserInfo.jrID.S,
              },
            },
            TableName: userTable,
            ExpressionAttributeValues: {
              ":mcID": {
                S: `${data.id}`,
              },
              ":mcURL": {
                S: data.management_url,
              },
              ":isPremium": {
                BOOL: true,
              },
              ":hasSub": {
                BOOL: true,
              },
              ":expDate": {
                S: data.subscription.current_period_end,
              },
            },
            UpdateExpression:
              "SET mcID = :mcID, isPremium = :isPremium, mcURL = :mcURL, expDate = :expDate, hasSubscription = :hasSub",
          })
          .then(() => {
            // Email them a "thank you" in 15 minutes
            sendEmail(data.email, 5, "<EMAIL>");

            // Update SiB
            fetch(
              "https://api.sendinblue.com/v3/contacts/" + jrUserInfo.sibID.S,
              {
                method: "PUT",
                headers: {
                  Accept: "application/json",
                  "Content-Type": "application/json",
                  "api-key": sibAPIKey,
                },
                body: {
                  attributes: {
                    HASPREMIUM: true,
                  },
                },
              }
            ).catch((err) => console.error("Update SIB user error", err));
          })
          .catch((err) => {
            console.error(
              "Update to premium DB error for request",
              req.query,
              err
            );
          });
      })
      .catch((err) => {
        console.error("Getting JR info based on email error", req.query, err);
      });

    res.status(200).end();
  });

  app.post("/hook/plan-ended", function (req, res) {
    const data = req.body.data;
    getJRUserInfoWithEmail(data.email)
      .then((jrUserInfo) => {
        ddb
          .updateItem({
            Key: {
              jrID: {
                S: jrUserInfo.jrID.S,
              },
            },
            TableName: userTable,
            ExpressionAttributeValues: {
              ":hasSub": {
                BOOL: false,
              },
            },
            UpdateExpression: "SET hasSubscription = :hasSub",
          })
          .catch((err) => {
            console.error(
              "Update to premium DB error for request",
              req.query,
              err
            );
          });
      })
      .catch((err) => {
        console.error("Getting JR info based on email error", req.query, err);
      });

    res.status(200).end();
  });

  app.post(
    "/stripe-endpoint",
    express.raw({ type: "application/json" }),
    async (req, res) => {
      const sig = req.headers["stripe-signature"];

      let event;

      try {
        event = stripe.webhooks.constructEvent(
          req.body,
          sig,
          stripeEndpointSecret
        );
      } catch (err) {
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }

      let customer, current_period_end;
      switch (event.type) {
        case "customer.created":
          const { id: stripeID, email } = event.data.object;
          try {
            const jrUserInfo = await getJRUserInfoWithEmail(email);
            await ddb.updateItem({
              Key: {
                jrID: {
                  S: jrUserInfo.jrID.S,
                },
              },
              TableName: userTable,
              ExpressionAttributeValues: {
                ":stripeID": {
                  S: `${stripeID}`,
                },
              },
              UpdateExpression: "SET stripeID = :stripeID",
            });
          } catch (err) {
            console.error("Customer created error", err);
          }
          break;
        case "customer.subscription.created":
          customer = event.data.object.customer;
          current_period_end = event.data.object.current_period_end * 1000;
          try {
            const jrUserInfo = await getUserInfoFromStripeID(customer);
            await ddb.updateItem({
              Key: {
                jrID: {
                  S: jrUserInfo.jrID.S,
                },
              },
              TableName: userTable,
              ExpressionAttributeValues: {
                ":isPremium": {
                  BOOL: true,
                },
                ":hasSubscription": {
                  BOOL: true,
                },
                ":expDate": {
                  S: new Date(current_period_end).toISOString(),
                },
              },
              UpdateExpression:
                "SET isPremium = :isPremium, hasSubscription = :hasSubscription, expDate = :expDate",
            });
            // Update SiB
            fetch(
              `https://api.sendinblue.com/v3/contacts/${jrUserInfo.sibID.S}`,
              {
                method: "PUT",
                headers: {
                  Accept: "application/json",
                  "Content-Type": "application/json",
                  "api-key": sibAPIKey,
                },
                body: {
                  attributes: {
                    HASPREMIUM: false,
                  },
                },
              }
            ).catch((err) => console.error("Update SIB user error", err));

            // Email them a "thank you" in 15 minutes
            sendEmail(jrUserInfo.email.S, 5, "<EMAIL>");
          } catch (err) {
            console.error("Subscription created error", err);
          }
          break;
        case "customer.subscription.deleted":
          customer = event.data.object.customer;
          try {
            const jrUserInfo = await getUserInfoFromStripeID(customer);
            await ddb.updateItem({
              Key: {
                jrID: {
                  S: jrUserInfo.jrID.S,
                },
              },
              TableName: userTable,
              ExpressionAttributeValues: {
                ":hasSubscription": {
                  BOOL: false,
                },
              },
              UpdateExpression: "SET hasSubscription = :hasSubscription",
            });
          } catch (err) {
            console.error("Subscription stopped error", err);
          }
          break;
        case "customer.subscription.updated":
          customer = event.data.object.customer;
          current_period_end = event.data.object.current_period_end * 1000;
          try {
            const jrUserInfo = await getUserInfoFromStripeID(customer);
            const oldDate = jrUserInfo.expDate.S;

            // Get the later time of the two
            const expDate =
              Date.parse(oldDate) > current_period_end
                ? oldDate
                : new Date(current_period_end).toISOString();
            await ddb.updateItem({
              Key: {
                jrID: {
                  S: jrUserInfo.jrID.S,
                },
              },
              TableName: userTable,
              ExpressionAttributeValues: {
                ":expDate": {
                  S: expDate,
                },
              },
              UpdateExpression: "SET expDate = :expDate",
            });
          } catch (err) {
            console.error("Subscription updated error", err);
          }
          break;
        default:
          console.log(`Unhandled Stripe event type ${event.type}`);
      }

      res.send();
    }
  );

  app.get("/:thePath", function (req, res, next) {
    if (req.params.thePath && req.params.thePath.indexOf(".") > -1) {
      return next();
    } else if (
      req.header("Accept") === "image/webp,image/apng,image/*,*/*;q=0.8"
    ) {
      // Prevent duplicate get requsts
      res.end();
    } else {
      const reqUrl =
        "https://" + req.get("host") + req.originalUrl.split(/[?#]/)[0];

      // Get the primary key of the entry related to the JR url
      getEntryFromUrl(reqUrl, async function (entry) {
        if (entry) {
          // plausible.trackPageview(req);

          const vc = parseInt(entry["viewcount"]["N"]);
          if (vc <= MAX_VIEW_COUNT) {
            const key =
              "saved/" + req.params.thePath.split(/[?#]/)[0] + ".html";

            // Retrieve the corresponding HTML from s3 and serve it
            const getCommand = new GetObjectCommand({
              Bucket: BUCKET_NAME,
              Key: key,
            });

            await s3
              .send(getCommand)
              .then(async (data) => {
                const stream = await data.Body?.transformToString("utf8");
                res.send(stream);
              })
              .catch((error) => {
                // Not in S3
                return next();
              });

            // Don't keep articles that have their viewcount maxed
            let newKeepVal = entry["keep"]["BOOL"];
            if (vc === MAX_VIEW_COUNT) newKeepVal = false;

            // Update the view count in our DB
            ddb.updateItem(
              {
                Key: {
                  UserID: {
                    S: entry["UserID"]["S"],
                  },
                  datetime: {
                    S: entry["datetime"]["S"],
                  },
                },
                TableName: entryTable,
                ExpressionAttributeValues: {
                  ":url": {
                    S: reqUrl,
                  },
                  ":num": {
                    N: "1",
                  },
                  ":keep": {
                    BOOL: newKeepVal,
                  },
                },
                ConditionExpression: "JRUrl = :url",
                UpdateExpression: "ADD viewcount :num  SET keep = :keep",
              },
              function (err, data) {
                if (err) {
                  const returnStatus = 500;

                  if (err.code === "ConditionalCheckFailedException") {
                    returnStatus = 409;
                  }

                  res.status(returnStatus).end();
                  console.error("Update view count DB error", err);
                } else {
                  // Updated DB correctly
                }
              }
            );
          } else {
            // Too many views
            // 429: too many requests
            // plausible.trackEvent("429", req);
            res.sendFile("/static/429.html", { root: __dirname });
          }
        } else {
          // Page not found in DB
          return next();
        }
      });
    }
  });

  app.get("/:filename", function (req, res, next) {
    if (fs.existsSync(req.params.filename)) {
      res.sendFile(req.params.filename, { root: __dirname });
    } else {
      return next();
    }
  });

  // Return a 404 if we don't recognize the format or no file found
  app.get("*", function (req, res) {
    // Disabling 404 tracking for now since I seem to be getting scraped a whole lot
    // plausible.trackEvent("404", req);
    res.sendFile("/static/404.html", { root: __dirname });
  });

  // Create an HTML file based on the request
  app.post("/newEntry", function (req, res) {
    res.setHeader("Access-Control-Allow-Origin", "*");

    if (
      typeof req.body.jrSecret === "undefined" ||
      (req.body.jrSecret && !req.body.jrSecret.startsWith("JR-"))
    )
      throw "Invalid submission";

    getUserInfoFromSecret(req.body.jrSecret).then((userInfo) => {
      const jrID = userInfo.jrID.S;

      getEntryCount(jrID).then((myCount) => {
        if (myCount < MAX_ENTRY_COUNT) {
          const id = shortid.generate(); // Create a random and unique ID

          const filepath = "saved/" + id + ".html";

          // Copy the template file
          copyFileFromToBucket(
            "jr-template.html",
            BUCKET_NAME,
            filepath,
            BUCKET_NAME,
            async function (err) {
              if (err) return console.error("S3 copy error ", err);

              const getCommand = new GetObjectCommand({
                Bucket: BUCKET_NAME,
                Key: filepath,
              });

              await s3
                .send(getCommand)
                .then(async function (data) {
                  // Replace what we need to in the template
                  let result = await data.Body?.transformToString("utf8");
                  result = result.replace(/REPLACE ME/g, req.body.content);
                  result = result.replace(
                    /Just Read article/g,
                    req.body.title + " | Just Read"
                  );

                  // Upload our new file to S3
                  const uploadCommand = new PutObjectCommand({
                    Bucket: BUCKET_NAME,
                    Key: filepath,
                    Body: result,
                    ACL: "public-read",
                  });
                  await s3
                    .send(uploadCommand)
                    .then((data) => {
                      // Create an entry in our DB for the file
                      const JRUrl = "https://justread.link/" + id;

                      const entry = {
                        UserID: {
                          S: jrID,
                        },
                        origURL: {
                          S: req.body.origURL,
                        },
                        JRUrl: {
                          S: JRUrl,
                        },
                        datetime: {
                          S: req.body.datetime,
                        },
                        title: {
                          S: req.body.title,
                        },
                        author: {
                          S: req.body.author,
                        },
                        viewcount: {
                          N: "0",
                        },
                        keep: {
                          BOOL: false,
                        },
                      };

                      ddb.putItem(
                        {
                          TableName: entryTable,
                          Item: entry,
                        },
                        function (err, data) {
                          if (err) {
                            const returnStatus = 500;

                            if (
                              err.code === "ConditionalCheckFailedException"
                            ) {
                              returnStatus = 409;
                            }

                            res.status(returnStatus).end();
                            console.error("Entry putItem error ", err);
                          } else {
                            // Added to DB successfully
                            // Send the JRUrl back to the client
                            res.end(JRUrl);
                          }
                        }
                      );
                    })
                    .catch((err) => {
                      console.error("S3 upload error ", err);
                    });
                })
                .catch((error) => {
                  console.error("S3 get template error ", err);
                });
            }
          );
        } else {
          // Too many entries
          res.status(428).end("Too many shared pages."); // Precondition Required error code
        }
      });
    });
  });

  app.post("/deleteEntry", function (req, res) {
    getUserInfoFromSecret(req.body.delkey).then((userInfo) => {
      // Delete it from the DB
      const params = {
        TableName: entryTable,
        Key: {
          UserID: {
            S: userInfo ? userInfo.jrID.S : req.body.delkey,
          },
          datetime: {
            S: req.body.datetime,
          },
        },
      };

      ddb.deleteItem(params, function (err, data) {
        if (err) {
          console.error("Delete item error", err);
          res.status(500).end();
        } else res.end();
      });

      // Delete it from S3
      s3.deleteObject(
        {
          Bucket: BUCKET_NAME,
          Key:
            "saved/" +
            req.body.JRUrl.split("https://justread.link/")[1] +
            ".html",
        },
        function (err, data) {
          if (err) console.error("S3 delete error ", err);
        }
      );
    });
  });

  const port = process.env.PORT || 443;

  const server = app.listen(port);
}
